/*
 * @Author: asdevao <EMAIL>
 * @Date: 2025-03-28 13:43:29
 * @LastEditors: asdevao <EMAIL>
 * @LastEditTime: 2025-04-03 09:27:51
 * @FilePath: \voice_presentation_sys_pad\minnanyu\app.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
//app.js
App({
  onLaunch: function () {
    // 版本检查在onShow中实现
  },
  onShow: function () {
    // 检查更新
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager()
      updateManager.onCheckForUpdate(function (res) {
        // 请求完新版本信息的回调
        console.log('检查更新结果：', res.hasUpdate)
      })

      updateManager.onUpdateReady(function () {
        wx.showModal({
          title: '发现新版本',
          content: '新版本已准备就绪，是否立即更新？',
          showCancel: false,
          success: function (res) {
            if (res.confirm) {
              updateManager.applyUpdate()
            }
          }
        })
      })

      updateManager.onUpdateFailed(function () {
        wx.showModal({
          title: '更新失败',
          content: '新版本下载失败，请检查网络后重试',
          showCancel: false
        })
      })
    }
  },
  getUserInfo: function (cb) {

  },
  globalData: {
    userInfo: null
  }
})