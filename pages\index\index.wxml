<!--pages/ruyin/ruyin.wxml-->
<view class="container">
  <!-- 上部内容区域 -->
  <view class="content-area">
    <!-- 识别结果历史区域 -->
    <view class="history-section">
      <view class="history-title">识别历史</view>
      <scroll-view scroll-y="{{historyResults.length > 0}}" class="history-content" show-scrollbar="true" enhanced="true" scroll-top="{{historyScrollTop}}" scroll-with-animation="true" bindscroll="onHistoryScroll">
        <view wx:if="{{historyResults.length === 0}}" class="history-text placeholder">暂无识别记录</view>
        <view wx:for="{{historyResults}}" wx:key="index" class="history-item">
          <view class="history-row">
            <text class="history-time">[{{item.time}}]</text>
            <text class="history-text">{{item.text}}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 分隔线 -->
    <view class="divider"></view>

    <!-- 实时识别区域 -->
    <view class="realtime-section">
      <view class="realtime-title">实时识别</view>
      <scroll-view scroll-y class="realtime-content" enhanced="true" show-scrollbar="true">
        <view wx:if="{{isanzhu}}" class="realtime-text placeholder">{{textcontent}}</view>
        <view wx:else class="realtime-text">{{textcontent}}</view>
      </scroll-view>
    </view>
  </view>

  <!-- 底部操作区域 -->
  <view class="bottom-area">
    <!-- 操作提示 -->
    <view class="maike">点击麦克风开始录音，再次点击结束</view>

    <!-- 录音按钮 -->
    <view class="allimg">
      <image src="../../images/icon_one.png" class="record-btn {{isluyin ? 'on' : ''}}" bindtap="toggleRecording"></image>
      <!-- 录音时的加载动画 -->
      <view class="loading-circle {{isluyin ? 'show' : ''}}" wx:if="{{isluyin}}">
        <view class="circle-spinner"></view>
        <view class="pulse-ring"></view>
      </view>
    </view>
  </view>
</view>