/**index.wxss**/
page {
  background: #efeff4;
  font-size: 14px;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: calc(100% - 30px);
  margin: 15px 15px 0px 15px;
  box-sizing: border-box;
  position: relative;
  justify-content: space-between; /* 元素之间均匀分布 */
}

/* 上部内容区域 */
.content-area {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
  /* 进一步限制内容区域高度，确保底部有更多空间 */
  max-height: calc(100% - 200px); /* 从180px增加到200px */
}

/* 识别历史区域 */
.history-section {
  background: #fff;
  border-radius: 8px;
  margin-bottom: 5px;
  /* 减少历史区域高度比例 */
  height: 60%; /* 从65%减少到60% */
  min-height: 110px; /* 从120px减少到110px */
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.history-title {
  padding: 8px 15px 6px 15px; /* 减小标题高度 */
  font-size: 16px;
  font-weight: bold;
  color: #333;
  border-bottom: 1px solid #f0f0f0;
  flex-shrink: 0;
}

.history-content {
  flex: 1;
  padding: 0;
  overflow: hidden;
  height: 0;
  box-sizing: border-box;
}

/* 微信小程序scroll-view滚动条样式 */
.history-content scroll-view {
  height: 100%;
  width: 100%;
}

/* 自定义滚动条样式（在支持的环境中） */
.history-content::-webkit-scrollbar {
  width: 4px;
}

.history-content::-webkit-scrollbar-track {
  background: transparent;
}

.history-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.history-content::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

.history-item {
  margin: 4px 15px;
  padding: 6px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #007aff;
  word-wrap: break-word;
  word-break: break-all;
  box-sizing: border-box;
  max-width: 100%;
  overflow: hidden;
}

.history-row {
  display: flex;
  align-items: flex-start;
  width: 100%;
}

.history-time {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
  margin-right: 5px;
  flex-shrink: 0;
}

.history-text {
  font-size: 13px;
  color: #333;
  line-height: 1.3;
  word-wrap: break-word;
  word-break: break-all;
  white-space: pre-wrap;
  flex: 1;
  overflow-wrap: break-word;
  hyphens: auto;
  padding: 0 15px;
}

.history-text.placeholder {
  color: #999;
  padding: 15px;
  text-align: left;
}

.history-empty {
  text-align: center;
  color: #999;
  font-size: 14px;
  margin-top: 60px;
  padding: 0 15px;
}

/* 分隔线 */
.divider {
  height: 1px;
  background: #e0e0e0;
  margin: 3px 0;
  flex-shrink: 0;
}

/* 实时识别区域 */
.realtime-section {
  background: #fff;
  border-radius: 8px;
  margin-bottom: 10px;
  /* 使用固定比例而非flex值 */
  height: 30%;
  min-height: 50px;
  display: flex;
  flex-direction: column;
}

.realtime-title {
  padding: 6px 15px 4px 15px; /* 减小标题高度 */
  font-size: 16px;
  font-weight: bold;
  color: #333;
  border-bottom: 1px solid #f0f0f0;
  flex-shrink: 0;
}

.realtime-content {
  flex: 1;
  padding: 0;
  overflow: hidden;
  height: 0;
  box-sizing: border-box;
}

/* 自定义滚动条样式（在支持的环境中） */
.realtime-content::-webkit-scrollbar {
  width: 4px;
}

.realtime-content::-webkit-scrollbar-track {
  background: transparent;
}

.realtime-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.realtime-content::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

.realtime-text {
  font-size: 13px; /* 从16px减小到13px，与历史记录文字大小一致 */
  color: #333;
  line-height: 1.3; /* 从1.5减小到1.3，与历史记录行高一致 */
  width: 100%;
  padding: 8px 15px; /* 调整内边距与历史记录一致 */
  word-wrap: break-word;
  word-break: break-all;
  white-space: pre-wrap;
  box-sizing: border-box;
}

.realtime-text.placeholder {
  color: #999;
}

/* 底部操作区域 */
.bottom-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 10px;
  /* 增加底部内边距 */
  padding-bottom: 70px; /* 从50px增加到70px */
  /* 增加底部区域高度 */
  height: 180px; /* 从160px增加到180px */
}

/* 状态图标容器 */
.status-container {
  position: relative;
  width: 100%;
  height: 140px; /* 从160px减小到140px */
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0; /* 不允许缩小 */
  margin-top: -50px; /* 从-40px改为-50px，让状态图标更上移 */
  margin-bottom: -40px; /* 从-30px改为-40px，让提示条更上移 */
  z-index: 5; /* 确保状态图标在正确的层级 */
}

.centerimg {
  width: 140px; /* 进一步增大图标尺寸 */
  height: 140px; /* 进一步增大图标尺寸 */
  position: absolute;
  top: 40%; /* 将GIF位置上移，从50%改为40% */
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10; /* 确保GIF显示在最上层 */
}

/* 操作提示区域 */
.maike {
  width: 85%;
  max-width: 320px;
  min-width: 260px;
  height: 36px;
  line-height: 36px;
  background-color: #fff;
  font-size: 14px;
  border-radius: 18px;
  text-align: center;
  color: #627994;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #e8e8e8;
  padding: 0 20px;
  box-sizing: border-box;
  word-wrap: break-word;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 15px;
}

/* 录音按钮容器 */
.allimg {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative; /* 添加相对定位，作为加载动画的参考点 */
}

/* 录音按钮样式 */
.record-btn {
  width: 110px;
  height: 110px;
  border-radius: 50%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  z-index: 2; /* 确保按钮在动画上层 */
}

.record-btn.on {
  animation: button-pulse 2s infinite;
  box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.2), 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 2px solid rgba(247, 246, 246, 0.7);
}

/* 录音时的加载动画 */
.loading-circle {
  position: absolute;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
  transform: translate(-4px, -2px); /* 向左上角微调：左移3px，上移1px */
}

.loading-circle.show {
  opacity: 1;
}

/* 转圈动画 */
.circle-spinner {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 3px solid transparent; 
  border-top-color: rgba(0, 122, 255, 0.6);
  border-radius: 50%;
  animation: spin 1.5s linear infinite;
}

/* 脉冲环动画 */
.pulse-ring {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 2px solid rgba(0, 122, 255, 0.4); 
  border-radius: 50%;
  animation: pulse-ring 2s ease-out infinite;
}

/* 转圈动画关键帧 */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 脉冲环动画关键帧 */
@keyframes pulse-ring {
  0% {
    transform: scale(0.98);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.02);
    opacity: 0.3;
  }
  100% {
    transform: scale(0.98);
    opacity: 0.7;
  }
}

/* 按钮脉冲动画关键帧 */
@keyframes button-pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.2),
      0 4px 12px rgba(0, 0, 0, 0.15);
  }
  50% {
    transform: scale(1.01);
    box-shadow: 0 0 0 5px rgba(255, 255, 255, 0.15),
      0 4px 12px rgba(0, 0, 0, 0.15);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.2),
      0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

/* 小屏幕设备优化 */
@media screen and (max-height: 600px) {
  .content-area {
    max-height: calc(100% - 170px); /* 从150px增加到170px */
  }

  .history-section {
    height: 55%; /* 从60%减少到55% */
    min-height: 90px; /* 从100px减少到90px */
  }

  .realtime-section {
    height: 35%;
    min-height: 40px;
  }

  .bottom-area {
    height: 160px; /* 从140px增加到160px */
    padding-bottom: 45px; /* 从30px增加到45px */
  }

  .maike {
    height: 30px;
    line-height: 30px;
    font-size: 12px;
    margin-bottom: 10px;
  }

  .record-btn {
    width: 85px;
    height: 85px;
  }

  .loading-circle {
    width: 93px; 
    height: 93px; 
    transform: translate(-2.5px, -0.8px); /* 小屏幕向左上角微调 */
  }
}

/* 中等屏幕设备优化 */
@media screen and (min-height: 601px) and (max-height: 800px) {
  .content-area {
    max-height: calc(100% - 190px); /* 从170px增加到190px */
  }

  .history-section {
    height: 60%; /* 从65%减少到60% */
    min-height: 110px; /* 从120px减少到110px */
  }

  .realtime-section {
    height: 30%;
    min-height: 50px;
  }

  .bottom-area {
    height: 170px; /* 从150px增加到170px */
    padding-bottom: 55px; /* 从40px增加到55px */
  }

  .record-btn {
    width: 100px;
    height: 100px;
  }

  .loading-circle {
    width: 107px; /* 从110px减小到107px，更贴合按钮 */
    height: 107px; /* 从110px减小到107px，更贴合按钮 */
    transform: translate(-2.8px, -0.9px); /* 中等屏幕向左上角微调 */
  }
}

/* 大屏幕设备优化 */
@media screen and (min-height: 801px) {
  .content-area {
    max-height: calc(100% - 210px); /* 从190px增加到210px */
  }

  .history-section {
    height: 65%; /* 从70%减少到65% */
    min-height: 150px; /* 从160px减少到150px */
  }

  .realtime-section {
    height: 25%;
    min-height: 60px;
  }

  .bottom-area {
    height: 200px; /* 从180px增加到200px */
    padding-bottom: 70px; /* 从50px增加到70px */
  }

  .record-btn {
    width: 110px;
    height: 110px;
  }

  .loading-circle {
    width: 118px; /* 从120px减小到118px，更贴合按钮 */
    height: 118px; /* 从120px减小到118px，更贴合按钮 */
    transform: translate(-3px, -1px); /* 大屏幕向左上角微调 */
  }
}
