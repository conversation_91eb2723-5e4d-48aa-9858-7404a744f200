"use strict";

var _Page;

function _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _nonIterableSpread(); }

function _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance"); }

function _iterableToArray(iter) { if (Symbol.iterator in Object(iter) || Object.prototype.toString.call(iter) === "[object Arguments]") return Array.from(iter); }

function _arrayWithoutHoles(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = new Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } }

function _readOnlyError(name) { throw new Error("\"" + name + "\" is read-only"); }

function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }

function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(source, true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(source).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }

function _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }

// pages / ruyin / ruyin.js
//获取应用实例
var app = getApp();

var common = require("./../../dist/js/common.js");

var tempFilePath;
Page((_Page = {
  data: {
    isshibie: false,
    isluyin: false,
    isshow: false,
    isanzhu: true,
    textcontent: "按住麦克风录入语音",
    socketTaskId: null,
    isConnected: false,
    reconnectCount: 0,
    maxReconnectCount: 3,
    useWebSocket: false,
    // 是否使用WebSocket
    wsRetryTimer: null,
    // 重试定时器
    isConnecting: false,
    // 是否正在连接中
    wsConnectionFailed: false,
    // 是否已经尝试过WebSocket连接并失败
    wsRecognitionCompleted: false,
    // 标记WebSocket是否成功完成识别
    currentText: "",
    // 当前识别的文本
    isStreaming: false,
    // 是否正在流式识别
    fullText: "",
    // 完整的识别文本
    httpModeReady: false,
    // 标记是否已准备好使用HTTP模式
    audioConfig: {
      sampleRate: 16000,
      // 采样率，与Flask项目保持一致
      numberOfChannels: 1,
      // 单声道
      encodeBitRate: 48000,
      // 编码比特率
      format: 'wav',
      // 音频格式
      frameSize: 1,
      // 帧大小
      bufferSize: 1280,
      // 每帧字节数
      maxBufferSize: 3840,
      // 最大缓冲区大小（3帧）
      sendInterval: 100,
      // 发送间隔（毫秒），参考Flask项目中的SendInterval
      minBufferSize: 640,
      // 最小缓冲区大小
      maxRetryCount: 3,
      // 最大重试次数
      retryInterval: 100 // 重试间隔（毫秒）

    },
    iosAudioConfig: {
      sampleRate: 16000,
      // 采样率
      numberOfChannels: 1,
      // 单声道
      encodeBitRate: 48000,
      // 编码比特率
      format: 'wav',
      // 音频格式
      frameSize: 1 // 帧大小

    },
    audioBuffer: [],
    // 音频数据缓冲区
    lastSendTime: 0,
    // 上次发送数据的时间
    pendingResults: [],
    // 待处理的识别结果
    isProcessing: false,
    // 是否正在处理识别结果
    lastEndTime: 0,
    // 记录最后一个识别结果的结束时间
    duplicateThreshold: 300,
    // 重复识别的阈值（毫秒）
    isBufferFull: false,
    // 缓冲区是否已满
    streamStatus: {
      isStreaming: false,
      // 是否正在流式识别
      isRecording: false,
      // 是否正在录音
      isConnected: false // WebSocket是否已连接

    },
    isButtonPressed: false,
    // 添加按钮按压状态
    keepAliveTimer: null,
    // 保持连接的定时器
    wsTimeoutTimer: null,
    // WebSocket超时定时器
    wsTimeoutDuration: 15000,
    // WebSocket超时时间（毫秒）
    lastWsResponseTime: 0,
    // 最后一次WebSocket响应时间
    isUserSpeaking: false,
    // 用户是否在说话
    audioDataSent: false,
    // 是否已发送音频数据
    lastAudioDataTime: 0,
    // 最后一次发送音频数据的时间
    isIOS: false,
    // 是否是iOS设备
    lastFrameTime: 0,
    frameReceived: false,
    frameCheckTimer: null,
    lastMessageTime: 0,
    // 添加最后收到消息的时间
    messageCount: 0,
    // 添加消息计数
    recorderState: 'idle',
    // 添加全局录音状态: idle, recording, paused, stopping
    // iOS流式识别配置
    iosStreamingConfig: {
      enabled: false,
      // 是否启用iOS流式识别
      recordingPath: "",
      // 当前录音文件路径
      isRecording: false,
      // 是否正在录音
      segmentDuration: 800,
      // 增加每段录音时长至800毫秒，减少分段数量提高准确率
      segmentTimer: null,
      // 分段定时器
      currentSegment: 0,
      // 当前段数
      maxSegments: 200,
      // 最大段数
      tempFilePaths: [],
      // 临时文件路径
      fullAudioPath: "",
      // 完整录音文件路径，用于最终识别
      recognitionText: "",
      // 当前显示的识别文本
      recognitionQueue: [],
      // 识别队列
      isProcessingQueue: false,
      // 是否正在处理队列
      pendingResults: [],
      // 待处理的识别结果
      recordingTimeout: null,
      // 录音超时定时器
      recordingDelay: 100,
      // 减少录音间隔延迟至100毫秒，加快识别响应
      safeToRecord: true,
      // 是否安全开始新录音
      useFullAudioRecognition: false,
      // 是否在结束时使用完整录音文件进行最终识别，默认关闭
      usePartialResults: true,
      // 是否使用部分识别结果
      showProgressAnimation: true,
      // 是否显示进度动画
      animationTimer: null,
      // 进度动画定时器
      animationText: "",
      // 进度动画文本
      animationDots: "",
      // 进度动画的点
      fullRecordingStarted: false,
      // 是否已开始完整录音
      // 新增优化参数
      useVAD: true,
      // 使用语音活动检测
      silenceDetectionEnabled: true,
      // 启用静音检测
      consecutiveSilenceCount: 0,
      // 连续静音计数
      silenceThreshold: 3,
      // 静音阈值
      adaptiveSegmentDuration: false,
      // 关闭自适应分段时长，使用固定时长更稳定
      minSegmentDuration: 500,
      // 减小最小分段时长至500毫秒，与普通话流识别一致
      maxSegmentDuration: 1000,
      // 减小最大分段时长至1000毫秒，与普通话流识别一致
      currentSegmentDuration: 800,
      // 当前分段时长800毫秒
      segmentAdjustStep: 100,
      // 分段调整步长（毫秒）
      realtimeMode: true,
      // 开启实时模式，优先响应速度
      highQualityMode: false,
      // 关闭高质量模式，优先流畅度
      recognitionErrorCount: 0,
      // 识别错误计数
      maxErrorRetry: 3,
      // 最大错误重试次数
      lastResultTime: 0,
      // 上次接收结果时间
      resultTimeout: 3000,
      // 结果超时时间（毫秒）
      currentBufferCount: 0,
      // 当前缓冲区数量
      processingLock: false,
      // 处理锁，防止并发处理导致问题
      overlapThreshold: 0.6,
      // 降低文本重叠匹配阈值，提高匹配率
      usePreBuffer: true,
      // 启用预缓冲，减少漏字
      preBufferSize: 3,
      // 预缓冲区大小
      useTextCache: true,
      // 启用文本缓存，改善合并效果
      textCache: [],
      // 文本缓存数组
      maxCacheSize: 5 // 最大缓存大小

    },
    // 添加Flask项目中的变量
    audioDataBuffer: [],
    // 音频数据缓冲区
    audioDataCount: 1,
    // 音频数据序号，与Flask项目保持一致
    audioDataEncBusy: 0,
    // 编码队列忙状态
    audioDataTime: 0,
    // 上次发送音频数据的时间
    audioDataNumber: 0,
    // 音频数据编号
    audioDataMaxNumber: 0,
    // 最大音频数据编号
    audioDataChunk: null,
    // 音频数据块
    wsConnectionClosed: false,
    // 添加新状态标记，表示连接未被主动关闭
    isProcessingRecording: false // 添加处理录音的标记

  },
  onLoad: function onLoad() {
    try {
      // 判断设备类型
      var systemInfo = wx.getSystemInfoSync();
      console.log('系统信息:', systemInfo);
      this.setData({
        isIOS: systemInfo.platform === 'ios',
        // 修改iOS的音频配置，与Flask项目普通话流识别保持一致
        iosAudioConfig: {
          sampleRate: 16000,
          // 16kHz采样率，与普通话识别一致
          numberOfChannels: 1,
          // 单声道
          encodeBitRate: 48000,
          // 修改为48kbps，满足微信小程序对iOS的要求（大于24000且小于96000）
          format: 'wav',
          // wav格式
          frameSize: 1 // 启用流式传输

        },
        isProcessingRecording: false // 添加处理录音的标记

      });
      console.log('当前设备类型:', systemInfo.platform); // 初始化录音管理器

      this.initRecorderManager();
    } catch (error) {
      console.error('onLoad初始化失败:', error);
      wx.showToast({
        title: '初始化失败，请重试',
        icon: 'none'
      });
    }
  },
  onUnload: function onUnload() {
    // 关闭WebSocket连接
    this.closeWebSocket(); // 清除所有定时器

    if (this.data.wsRetryTimer) {
      clearTimeout(this.data.wsRetryTimer);
    } // 清除WebSocket超时定时器


    if (this.data.wsTimeoutTimer) {
      clearTimeout(this.data.wsTimeoutTimer);
    } // 清除帧检测定时器


    if (this.data.frameCheckTimer) {
      clearInterval(this.data.frameCheckTimer);
    } // 清除保活定时器


    if (this.data.keepAliveTimer) {
      clearInterval(this.data.keepAliveTimer);
    } // 清除iOS流式识别相关定时器


    if (this.data.iosStreamingConfig) {
      // 清除分段定时器
      if (this.data.iosStreamingConfig.segmentTimer) {
        clearInterval(this.data.iosStreamingConfig.segmentTimer);
      } // 清除动画定时器


      if (this.data.iosStreamingConfig.animationTimer) {
        clearInterval(this.data.iosStreamingConfig.animationTimer);
      } // 清除录音超时定时器


      if (this.data.iosStreamingConfig.recordingTimeout) {
        clearTimeout(this.data.iosStreamingConfig.recordingTimeout);
      }
    } // 清除iOS WebSocket流式传输定时器


    if (this.data.iosWebSocketTimer) {
      clearInterval(this.data.iosWebSocketTimer);
    } // 停止录音（如果正在录音）


    if (this.recorderManager && this.data.recorderState === 'recording') {
      try {
        this.recorderManager.stop();
      } catch (error) {
        console.error('停止录音失败:', error);
      }
    } // 清理临时文件


    this.cleanupTempFiles().then(function () {
      console.log('页面卸载时临时文件清理完成');
    })["catch"](function (error) {
      console.error('页面卸载时清理临时文件失败:', error);
    });
    console.log('页面卸载，已清理所有资源');
  },
  connectWebSocket: function connectWebSocket() {
    var that = this;
    var sid = parseInt(Math.random() * 3000).toString(); // 每个用户取3000内的随机值，参考Flask项目

    return new Promise(function (resolve, reject) {
      // 如果已经存在连接，先关闭它
      if (that.data.socketTaskId) {
        try {
          that.data.socketTaskId.close({
            complete: function complete() {
              that.setData({
                socketTaskId: null,
                isConnected: false
              });
              console.log('已关闭现有WebSocket连接');
            }
          });
        } catch (e) {
          console.error('关闭现有WebSocket连接失败:', e);
          that.setData({
            socketTaskId: null,
            isConnected: false
          });
        }
      } // 重置重连计数


      that.setData({
        reconnectCount: 0,
        isConnecting: true,
        wsConnectionClosed: false // 添加新状态标记，表示连接未被主动关闭

      });

      try {
        console.log('尝试创建WebSocket连接', {
          timestamp: new Date().toISOString(),
          sid: sid,
          isIOS: that.data.isIOS
        }); // 使用不同的凭证和URL

        var userid, token; // 安卓设备使用ts_demo/ts_demo凭证，与公司demo保持一致

        if (!that.data.isIOS) {
          userid = "ts_demo";
          token = "ts_demo";
        } else {
          // iOS设备继续使用test123/test1凭证
          userid = "test123";
          token = "test1";
        } // 使用闽南语专用WebSocket接口


        var wsUrl = "wss://mny.talentedsoft.com/dotcwsasr?userid=" + userid + "&token=" + token + "&sid=" + sid;
        console.log('使用闽南语WebSocket接口:', wsUrl);
        var socketTask = wx.connectSocket({
          url: wsUrl,
          success: function success(res) {
            console.log('WebSocket连接创建成功', {
              timestamp: new Date().toISOString()
            });
          },
          fail: function fail(error) {
            console.error('WebSocket连接创建失败：', error);
            that.setData({
              isConnecting: false
            });
            reject(error);
          }
        });

        if (!socketTask) {
          console.error('创建WebSocket连接失败，socketTask为空');
          that.setData({
            isConnecting: false
          });
          reject(new Error('创建WebSocket连接失败'));
          return;
        } // 保存socketTask对象并初始化相关状态


        that.setData({
          socketTaskId: socketTask,
          lastMessageTime: 0,
          // 添加最后收到消息的时间
          messageCount: 0,
          // 添加消息计数
          // 重置文本缓存以便更好地合并文本
          currentText: "",
          fullText: ""
        }); // 注册WebSocket事件处理函数

        socketTask.onOpen(function () {
          console.log('WebSocket连接已打开', {
            timestamp: new Date().toISOString(),
            isIOS: that.data.isIOS
          }); // 再次检查socketTask是否仍然存在，使用本地变量而非data中的值

          if (!socketTask) {
            console.error('WebSocket连接已打开，但socketTask已不存在');
            reject(new Error('WebSocket状态不一致'));
            return;
          } // 检查连接是否已被关闭


          if (that.data.wsConnectionClosed) {
            console.log('WebSocket连接已打开，但已被标记为关闭状态，忽略此连接');

            try {
              socketTask.close();
            } catch (e) {
              console.error('关闭多余WebSocket连接失败:', e);
            }

            reject(new Error('WebSocket已被关闭'));
            return;
          }

          that.setData({
            isConnected: true,
            isConnecting: false,
            lastReceivedTime: Date.now(),
            // 更新最后接收消息时间
            heartbeatMisses: 0,
            // 重置心跳失败计数
            streamStatus: _objectSpread({}, that.data.streamStatus, {
              isConnected: true,
              isStreaming: true
            })
          }); // 启动保活机制

          that.startKeepAlive(); // 移除启动WebSocket超时检测

          resolve();
        });
        socketTask.onMessage(function (res) {
          // 无论消息内容如何，更新最后接收消息时间
          that.setData({
            lastReceivedTime: Date.now(),
            heartbeatMisses: 0 // 收到任何消息都重置心跳失败计数

          });

          if (!that.data.isButtonPressed) {
            console.log('按钮已松开，不处理WebSocket消息');
            return;
          } // 检查连接是否已被关闭


          if (that.data.wsConnectionClosed) {
            console.log('WebSocket连接已被标记为关闭状态，忽略此消息');
            return;
          } // 检查是否是心跳响应


          if (res.data === "pong" || res.data === '{"type":"heartbeat"}') {
            console.log('收到心跳响应');
            return;
          }

          try {
            // 更新消息计数
            that.setData({
              lastMessageTime: Date.now(),
              messageCount: that.data.messageCount + 1
            });
            console.log('收到WebSocket消息', {
              timestamp: new Date().toISOString(),
              messageLength: res.data ? res.data.length : 0,
              messageCount: that.data.messageCount,
              isIOS: that.data.isIOS
            }); // 处理特殊字符

            var newStr;

            if (typeof res.data === 'string') {
              newStr = res.data.replace(/\n/g, "~");
            } else {
              console.log('收到非文本消息，可能是二进制数据');
              return;
            }

            var ret_json;

            try {
              ret_json = JSON.parse(newStr);
              console.log('解析后的WebSocket消息:', ret_json); // 文本预处理

              if (ret_json.result) {
                var content = ret_json.result; // 替换特殊字符

                content = content.replace(/~/g, "\n");
                content = content.replace(/\|/g, " ");
                ret_json.result = content;
              }
            } catch (parseError) {
              console.error('解析WebSocket消息失败:', parseError, '原始数据:', newStr); // 尝试直接处理文本内容（安卓设备可能不是标准JSON格式）

              if (newStr && !that.data.isIOS) {
                // 尝试直接从字符串中提取结果
                var resultMatch = newStr.match(/result\s*:\s*["']([^"']*)["']/i);

                if (resultMatch && resultMatch[1]) {
                  var textResult = resultMatch[1].replace(/~/g, "\n").replace(/\|/g, " "); // 创建一个简单的结果对象

                  ret_json = {
                    result: textResult,
                    errCode: "0" // 假设为最终结果

                  };
                } else {
                  return;
                }
              } else {
                return;
              }
            } // 使用改进后的处理函数处理识别结果


            that.processRecognitionResult(ret_json);
          } catch (error) {
            console.error('处理WebSocket消息失败：', error, '原始数据:', res.data);
          }
        });
        socketTask.onError(function (error) {
          console.error('WebSocket错误：', error, {
            isIOS: that.data.isIOS,
            timestamp: new Date().toISOString()
          });
          that.setData({
            isConnecting: false
          }); // 处理WebSocket错误

          that.handleWebSocketError(error);

          if (that.data.isButtonPressed) {
            // 如果按钮仍然按下，尝试重连
            that.reconnectWebSocket();
          }

          reject(error);
        });
        socketTask.onClose(function (res) {
          console.log('WebSocket连接已关闭', {
            timestamp: new Date().toISOString(),
            isIOS: that.data.isIOS,
            messageCount: that.data.messageCount,
            lastMessageTime: that.data.lastMessageTime ? new Date(that.data.lastMessageTime).toISOString() : '无',
            code: res.code,
            reason: res.reason
          });
          that.setData({
            isConnected: false,
            isConnecting: false,
            socketTaskId: null
          }); // 只有在按钮仍然按下且连接未被主动关闭时才尝试重连

          if (that.data.isButtonPressed && !that.data.wsConnectionClosed) {
            that.reconnectWebSocket();
          }
        });
      } catch (error) {
        console.error('创建WebSocket连接时出错：', error);
        that.setData({
          isConnecting: false
        });
        reject(error);
      }
    });
  },
  // 添加处理识别结果的函数
  processRecognitionResult: function processRecognitionResult(result) {
    var that = this; // 检查结果是否有效

    if (!result) {
      console.warn('收到空的识别结果');
      return;
    }

    console.log('处理识别结果:', result); // 处理识别结果

    if (result.result) {
      // 获取识别文本
      var newText = result.result;
      var errCode = result.errCode || "0";
      console.log('%c[WebSocket识别] 识别结果: ' + newText, 'color: #FF9800; font-weight: bold;', '错误码:', errCode);

      if (errCode === "3") {
        // 中间结果
        // 如果当前没有完整文本，直接显示中间结果
        if (!that.data.fullText) {
          that.setData({
            currentText: newText,
            textcontent: newText
          });
        } else {
          // 如果已有完整文本，将中间结果附加到完整文本后
          that.setData({
            currentText: newText,
            textcontent: that.data.fullText + ' ' + newText
          });
        }
      } else {
        // 最终结果 - 添加else块明确区分
        // 将当前文本添加到完整文本中
        var newFullText = that.data.fullText;
        var date = new Date();

        if (newFullText && newText) {
          // 如果已有完整文本，尝试智能合并
          if (that.smartMergeText && typeof that.smartMergeText === 'function') {
            // 使用智能合并函数
            newFullText = that.smartMergeText(newFullText, newText);
          } else {
            // 如果智能合并不可用，添加空格再拼接
            newFullText = newFullText + ' ' + newText;
          }
        } else {
          // 如果是第一段文本，直接使用
          newFullText = newText;
        } // 参考Flask项目，添加时间戳


        console.log('最终识别结果 [' + that.dateFormat("YYYY-mm-dd HH:MM", date) + ']: ' + newFullText);
        that.setData({
          currentText: '',
          fullText: newFullText,
          textcontent: newFullText
        }); // 只有在最终结果时，才标记 WebSocket 识别完成

        if (result.isEnd === true || result.End) {
          that.setData({
            wsRecognitionCompleted: true
          });
          console.log('WebSocket识别完成');
        }
      }
    } else if (result.errCode && result.errCode !== "0" && result.errCode !== "3") {
      console.warn('识别错误:', result.errCode); // 对于 iOS 设备，错误码为 -1 时不要中断识别流程

      if (that.data.isIOS && result.errCode === "-1") {
        console.log('iOS 设备收到错误码 -1，忽略此错误继续识别');
        return;
      }
    }
  },
  handleWebSocketError: function handleWebSocketError(error) {
    var that = this;
    console.error('处理WebSocket错误：', {
      error: error,
      socketTaskId: that.data.socketTaskId ? '存在' : '不存在',
      timestamp: new Date().toISOString(),
      status: that.data.streamStatus,
      errorType: error.errMsg ? 'wx.error' : 'js.error',
      isIOS: that.data.isIOS
    }); // 关闭现有连接（如果存在）

    if (that.data.socketTaskId) {
      try {
        that.data.socketTaskId.close({
          complete: function complete() {
            that.setData({
              socketTaskId: null,
              isConnected: false
            });
            console.log('因错误关闭WebSocket连接');
          }
        });
      } catch (closeError) {
        console.error('关闭WebSocket连接时出错：', closeError);
        that.setData({
          socketTaskId: null,
          isConnected: false
        });
      }
    } else {
      // 如果socketTaskId不存在，也需要确保状态正确
      that.setData({
        socketTaskId: null,
        isConnected: false
      });
    } // 无论是iOS还是Android，都尝试重新连接WebSocket


    if (that.data.isButtonPressed && that.data.reconnectCount < that.data.maxReconnectCount) {
      console.log('稍后将尝试重连WebSocket'); // 重连将由reconnectWebSocket函数处理

      that.reconnectWebSocket();
    } else {
      // 达到重连次数上限，但仍然保持WebSocket模式
      that.setData({
        socketTaskId: null,
        isConnected: false,
        isConnecting: false,
        isStreaming: false,
        wsConnectionFailed: true,
        reconnectCount: 0,
        // 重置重连计数，以便下次可以再次尝试
        streamStatus: {
          isStreaming: false,
          isRecording: false,
          isConnected: false
        }
      }); // 显示提示

      wx.showToast({
        title: '连接失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  },
  // 修改closeWebSocket函数，确保像Flask项目一样先发送end标记再关闭连接
  closeWebSocket: function closeWebSocket() {
    var forceClose = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;
    var that = this; // 标记连接为已关闭状态，防止自动重连

    that.setData({
      wsConnectionClosed: true
    });

    if (that.data.socketTaskId) {
      // 保存当前的识别文本
      var finalText = that.data.fullText || that.data.textcontent;

      try {
        // 如果是强制关闭，直接关闭不发送任何结束标记
        if (forceClose) {
          console.log('强制关闭WebSocket连接');

          try {
            // 直接关闭连接
            that.data.socketTaskId.close({
              success: function success() {
                console.log('WebSocket连接已强制关闭');
              },
              fail: function fail(error) {
                console.log('强制关闭WebSocket连接失败:', error);
              },
              complete: function complete() {
                // 重置连接状态，但保留当前文本
                that.setData({
                  socketTaskId: null,
                  isConnected: false,
                  isConnecting: false,
                  isStreaming: false,
                  reconnectCount: 0,
                  // 重置重连计数
                  textcontent: finalText,
                  // 保持最终文本
                  streamStatus: {
                    isStreaming: false,
                    isRecording: false,
                    isConnected: false
                  }
                });
              }
            });
          } catch (closeError) {
            console.error('强制关闭WebSocket连接时出错：', closeError); // 出错时也重置状态

            that.setData({
              socketTaskId: null,
              isConnected: false,
              isConnecting: false,
              isStreaming: false,
              reconnectCount: 0,
              // 重置重连计数
              textcontent: finalText,
              // 保持最终文本
              streamStatus: {
                isStreaming: false,
                isRecording: false,
                isConnected: false
              }
            });
          }

          return;
        } // 先发送结束标记，就像Flask项目那样


        console.log('发送WebSocket结束标记');
        that.data.socketTaskId.send({
          data: 'end',
          success: function success() {
            console.log('发送结束标记成功'); // 成功发送end标记后关闭连接

            setTimeout(function () {
              try {
                that.data.socketTaskId.close({
                  success: function success() {
                    console.log('WebSocket连接已关闭');
                  },
                  fail: function fail(error) {
                    console.log('关闭WebSocket连接失败:', error);
                  },
                  complete: function complete() {
                    // 重置连接状态，但保留当前文本
                    that.setData({
                      socketTaskId: null,
                      isConnected: false,
                      isConnecting: false,
                      isStreaming: false,
                      reconnectCount: 0,
                      // 重置重连计数
                      textcontent: finalText,
                      // 保持最终文本
                      streamStatus: {
                        isStreaming: false,
                        isRecording: false,
                        isConnected: false
                      }
                    });
                  }
                });
              } catch (closeError) {
                console.error('关闭WebSocket连接时出错：', closeError); // 出错时也重置状态

                that.setData({
                  socketTaskId: null,
                  isConnected: false,
                  isConnecting: false,
                  isStreaming: false,
                  reconnectCount: 0,
                  // 重置重连计数
                  textcontent: finalText,
                  // 保持最终文本
                  streamStatus: {
                    isStreaming: false,
                    isRecording: false,
                    isConnected: false
                  }
                });
              }
            }, 200); // 等待200ms确保end标记已发送
          },
          fail: function fail(error) {
            console.error('发送结束标记失败：', error); // 发送失败也尝试关闭连接

            that.data.socketTaskId.close({
              complete: function complete() {
                that.setData({
                  socketTaskId: null,
                  isConnected: false,
                  isConnecting: false,
                  isStreaming: false,
                  reconnectCount: 0,
                  textcontent: finalText,
                  streamStatus: {
                    isStreaming: false,
                    isRecording: false,
                    isConnected: false
                  }
                });
              }
            });
          }
        });
      } catch (error) {
        console.error('处理WebSocket关闭时出错：', error); // 出错时也重置状态

        that.setData({
          socketTaskId: null,
          isConnected: false,
          isConnecting: false,
          isStreaming: false,
          reconnectCount: 0,
          // 重置重连计数
          textcontent: finalText,
          // 保持最终文本
          streamStatus: {
            isStreaming: false,
            isRecording: false,
            isConnected: false
          }
        });
      }
    } else {
      console.log('WebSocket未连接或已关闭，无需关闭操作'); // 确保重置状态

      that.setData({
        socketTaskId: null,
        isConnected: false,
        isConnecting: false,
        isStreaming: false,
        reconnectCount: 0,
        streamStatus: {
          isStreaming: false,
          isRecording: false,
          isConnected: false
        }
      });
    }
  },
  tip: function tip(msg) {
    wx.showModal({
      title: '提示',
      content: msg,
      showCancel: false,
      isshibie: false
    });
  },
  // 修改录音管理器初始化方法，增强错误处理
  initRecorderManager: function initRecorderManager() {
    var that = this; // 如果已经存在录音管理器，先尝试释放

    if (that.recorderManager) {
      try {
        // 尝试停止可能正在进行的录音
        if (that.data.recorderState === 'recording') {
          that.recorderManager.stop();
        } // 解绑事件监听，避免重复监听


        if (that.recorderManager.offStart) that.recorderManager.offStart();
        if (that.recorderManager.offStop) that.recorderManager.offStop();
        if (that.recorderManager.offError) that.recorderManager.offError();
        if (that.recorderManager.offFrameRecorded) that.recorderManager.offFrameRecorded();
        console.log('释放现有录音管理器');
      } catch (error) {
        console.error('释放录音管理器出错:', error);
      }
    } // 创建新的录音管理器


    that.recorderManager = wx.getRecorderManager(); // 添加iOS设备的帧数据检测

    that.setData({
      lastFrameTime: 0,
      frameReceived: false,
      frameCheckTimer: null,
      recorderState: 'idle' // 初始化录音状态

    }); // 添加offStop方法，如果不存在则创建一个空方法

    if (!that.recorderManager.offStop) {
      that.recorderManager.offStop = function () {
        console.log('自定义offStop方法被调用'); // 这是一个空方法，仅用于兼容性
      };
    }

    that.recorderManager.onFrameRecorded(function (res) {
      // 添加日志确认回调触发
      console.log('onFrameRecorded触发', {
        hasData: !!res.frameBuffer,
        dataSize: res.frameBuffer ? res.frameBuffer.byteLength : 0,
        timestamp: new Date().toISOString()
      }); // 标记已收到帧数据

      that.setData({
        frameReceived: true,
        lastFrameTime: Date.now()
      }); // 如果是iOS设备，不处理帧数据

      if (that.data.isIOS) {
        console.log('iOS设备不处理onFrameRecorded回调');
        return;
      }

      if (!that.data.isButtonPressed || !that.data.socketTaskId) return;
      var frameBuffer = res.frameBuffer,
          isLastFrame = res.isLastFrame; // 输出音频帧信息

      console.log('收到音频帧数据:', {
        frameSize: frameBuffer ? frameBuffer.byteLength : 0,
        isLastFrame: isLastFrame,
        timestamp: new Date().toISOString()
      });

      if (!frameBuffer || !frameBuffer.byteLength) {
        console.warn('音频帧数据为空或长度为0');
        return;
      } // 更新音频数据发送状态


      that.setData({
        audioDataSent: true,
        lastAudioDataTime: Date.now()
      }); // 对于安卓设备，直接发送音频数据，不使用processAudioData

      that.data.socketTaskId.send({
        data: frameBuffer,
        success: function success() {
          console.log('发送音频数据成功，数据长度:', frameBuffer.byteLength, '字节');
        },
        fail: function fail(error) {
          console.error('发送音频数据失败：', error);

          if (that.data.isButtonPressed) {
            that.reconnectWebSocket();
          }
        }
      });
    });
    that.recorderManager.onStart(function () {
      console.log('录音开始', {
        timestamp: new Date().toISOString(),
        isIOS: that.data.isIOS,
        config: that.data.isIOS ? that.data.iosAudioConfig : that.data.audioConfig
      }); // 更新全局录音状态

      that.setData({
        recorderState: 'recording'
      });
    });
    that.recorderManager.onStop(function (res) {
      console.log('录音停止', {
        timestamp: new Date().toISOString(),
        duration: res.duration,
        fileSize: res.fileSize,
        tempFilePath: res.tempFilePath ? '有临时文件' : '无临时文件',
        recorderState: that.data.recorderState,
        isIOS: that.data.isIOS,
        isButtonPressed: that.data.isButtonPressed,
        iosStreamingEnabled: that.data.isIOS ? that.data.iosStreamingConfig.enabled : false,
        useWebSocket: that.data.useWebSocket
      }); // 更新全局录音状态

      that.setData({
        recorderState: 'idle'
      }); // 根据不同的模式处理录音结果

      if (that.data.isIOS) {
        if (that.data.useWebSocket && that.data.socketTaskId) {
          // 处理WebSocket流式传输
          that.handleWebSocketRecorderStop(res);
          return;
        } else if (that.data.iosStreamingConfig.enabled && res.tempFilePath) {
          // 处理分段HTTP录音
          var currentSegment = that.data.iosStreamingConfig.currentSegment;
          that.handleRecorderStop(res, currentSegment);
          return;
        }
      } // 如果WebSocket连接失败或未使用WebSocket，则使用HTTP方式处理


      if ((that.data.wsConnectionFailed || !that.data.useWebSocket) && res.tempFilePath && !that.data.isIOS) {
        console.log('使用HTTP方式处理录音文件');
        that.handleHttpRecognition(res.tempFilePath);
      } // 不要在这里修改文本内容，保持最后的识别结果


      that.setData({
        isluyin: false,
        isStreaming: false,
        streamStatus: {
          isStreaming: false,
          isRecording: false,
          isConnected: false
        }
      });
    });
    that.recorderManager.onError(function (error) {
      console.error('录音错误：', error, {
        recorderState: that.data.recorderState,
        isButtonPressed: that.data.isButtonPressed,
        isProcessingRecording: that.data.isProcessingRecording
      }); // 更新全局录音状态

      that.setData({
        recorderState: 'idle',
        isProcessingRecording: false // 确保处理标记被清除

      }); // 如果是由于录音未开始导致的错误，不显示错误提示

      if (error.errMsg && error.errMsg.indexOf('recorder not start') !== -1) {
        console.log('录音未开始，忽略错误');
        return;
      } // 如果是由于录音已开始导致的错误，也不显示错误提示


      if (error.errMsg && error.errMsg.indexOf('recorder already start') !== -1) {
        console.log('录音已经开始，忽略错误');

        try {
          that.recorderManager.stop();
          setTimeout(function () {
            if (that.data.isButtonPressed) {
              that.recorderManager.start(that.data.isIOS ? that.data.iosAudioConfig : that.data.audioConfig);
            }
          }, 300);
        } catch (e) {
          console.error('尝试重新启动录音失败:', e);
        }

        return;
      }

      wx.showToast({
        title: '录音出错，请重试',
        icon: 'none'
      });
      that.closeWebSocket();
      that.setData({
        isluyin: false,
        isanzhu: true,
        textcontent: '按住麦克风录入语音',
        isButtonPressed: false
      });
    });
  },
  // 新增HTTP识别处理方法
  handleHttpRecognition: function handleHttpRecognition(tempFilePath) {
    var that = this;
    var url = "https://mny.talentedsoft.com/dotcasr";
    var formData = {
      "userid": "tc_wx_yun7",
      "token": "wx5678"
    };

    if (that.data.currentClient == "windows") {
      formData["asr_type"] = "minnanyu";
      url = (_readOnlyError("url"), "https://tc.talentedsoft.com/ajax/wechat/dotcasr_proxy");
    } // 显示识别中动画，确保只显示shibie.gif


    that.setData({
      'isshow': false,
      'isshibie': true,
      // 设置为true显示shibie.gif
      'textcontent': '正在识别中...',
      'isluyin': false,
      // 设置为false表示不在录音状态
      'isanzhu': false // 设置为false不显示tipimg.png

    });
    wx.uploadFile({
      url: url,
      filePath: tempFilePath,
      header: {
        "Content-Type": "multipart/form-data"
      },
      name: 'file',
      formData: formData,
      success: function success(res) {
        console.log('HTTP识别结果：', res.data);

        if (res.data) {
          try {
            var data = JSON.parse(res.data);

            if (data.errCode == "0") {
              if (!data.result) {
                that.setData({
                  'isanzhu': false,
                  // 保持为false
                  'isshibie': false,
                  'textcontent': '按住麦克风录入语音',
                  'isshow': true
                });
                setTimeout(function () {
                  that.setData({
                    'isshow': false
                  });
                }, 2000);
              } else {
                that.setData({
                  'isanzhu': false,
                  'isshibie': false,
                  'textcontent': data.result,
                  'isshow': false
                });
              }
            } else {
              that.setData({
                'isanzhu': false,
                // 保持为false
                'textcontent': '按住麦克风录入语音',
                'isshibie': false,
                'isshow': true
              });
              setTimeout(function () {
                that.setData({
                  'isshow': false
                });
              }, 2000);
            }
          } catch (error) {
            console.error('解析识别结果失败：', error);
            that.handleRecognitionError();
          }
        }
      },
      fail: function fail(err) {
        console.error('HTTP识别请求失败：', err);
        that.handleRecognitionError();
      }
    });
  },
  // 修改错误处理方法
  handleRecognitionError: function handleRecognitionError() {
    var that = this;
    wx.hideLoading();
    wx.showToast({
      title: "识别服务请求异常，请重试",
      icon: "none",
      duration: 2000
    });
    that.setData({
      'isanzhu': false,
      // 设置为false不显示tipimg.png
      'textcontent': '按住麦克风录入语音',
      'isshibie': false,
      'isshow': false
    });
  },
  onShow: function onShow() {
    var that = this;

    try {
      wx.showShareMenu({
        withShareTicket: true
      }); // 检查接口是否可用

      wx.getSetting({
        success: function success(res) {
          console.log('录音权限状态:', res['scope.record']);

          if (!res['scope.record']) {
            // 接口调用询问
            wx.authorize({
              scope: 'scope.record',
              success: function success(res) {
                console.log('录音权限授权成功');
                that.initRecorderManager();
              },
              fail: function fail(error) {
                console.error('录音权限授权失败:', error);
                wx.showModal({
                  title: '提示',
                  content: '需要录音权限才能使用语音识别功能',
                  showCancel: false,
                  success: function success() {
                    wx.openSetting({
                      success: function success(res) {
                        console.log('打开设置页面成功');

                        if (res.authSetting['scope.record']) {
                          that.initRecorderManager();
                        }
                      },
                      fail: function fail(error) {
                        console.error('打开设置页面失败:', error);
                      }
                    });
                  }
                });
              }
            });
          } else {
            console.log('已有录音权限，初始化录音管理器');
            that.initRecorderManager();
          }
        },
        fail: function fail(error) {
          console.error('获取录音权限状态失败:', error);
          wx.showToast({
            title: '获取权限失败，请重试',
            icon: 'none'
          });
        }
      });
    } catch (error) {
      console.error('onShow执行失败:', error);
      wx.showToast({
        title: '启动失败，请重试',
        icon: 'none'
      });
    }
  },
  // 开始录音（按下按钮）
  startRecord: function startRecord() {
    var that = this; // 如果正在录音，先停止

    if (that.data.isluyin) {
      that.stopRecord();
      return;
    } // 检查录音状态，只有在idle状态才开始新录音


    if (that.data.recorderState !== 'idle') {
      console.log('录音管理器不在空闲状态，等待录音状态重置', {
        timestamp: new Date().toISOString(),
        recorderState: that.data.recorderState
      }); // 尝试停止现有录音

      try {
        that.recorderManager.stop();
      } catch (error) {
        console.error('停止现有录音失败:', error);
      } // 重置录音状态


      that.setData({
        recorderState: 'idle'
      }); // 延迟一点开始新录音

      setTimeout(function () {
        that.startRecord();
      }, 300);
      return;
    } // 清空之前的文本和状态


    that.setData({
      'currentText': '',
      'fullText': '',
      'textcontent': '正在录音...',
      'audioBuffer': [],
      'pendingResults': [],
      'lastEndTime': 0,
      'isBufferFull': false,
      'wsConnectionFailed': false,
      'wsRecognitionCompleted': false,
      'useWebSocket': true,
      'isluyin': true,
      'isanzhu': false,
      'isshibie': false,
      'isshow': false,
      streamStatus: {
        isStreaming: false,
        isRecording: true,
        isConnected: false
      }
    }); // 确保录音管理器已初始化

    if (!that.recorderManager) {
      that.initRecorderManager();
    } // 设置录音参数


    var options = that.data.audioConfig.sampleRate ? {
      sampleRate: that.data.audioConfig.sampleRate,
      numberOfChannels: that.data.audioConfig.numberOfChannels,
      encodeBitRate: that.data.audioConfig.encodeBitRate,
      format: that.data.audioConfig.format,
      frameSize: that.data.audioConfig.frameSize
    } : {
      sampleRate: 16000,
      numberOfChannels: 1,
      encodeBitRate: 48000,
      format: 'wav',
      frameSize: 1
    };

    try {
      // 创建WebSocket连接
      that.connectWebSocket().then(function () {
        // 成功连接后开始录音
        try {
          that.recorderManager.start(options);
          console.log('开始录音和识别...');
        } catch (recError) {
          console.error('开始录音失败:', recError);
          wx.showToast({
            title: '启动录音失败，请重试',
            icon: 'none'
          });
          that.closeWebSocket();
          that.setData({
            'isluyin': false,
            'isanzhu': true,
            'textcontent': '按住麦克风录入语音',
            streamStatus: {
              isStreaming: false,
              isRecording: false,
              isConnected: false
            }
          });
        }
      })["catch"](function (error) {
        // WebSocket连接失败，使用HTTP模式
        console.error('WebSocket连接失败，将使用HTTP方式：', error);
        that.setData({
          streamStatus: {
            isStreaming: false,
            isRecording: true,
            isConnected: false
          },
          useWebSocket: false
        });

        try {
          that.recorderManager.start(options);
          console.log('开始录音(HTTP模式)...');
        } catch (recError) {
          console.error('开始录音(HTTP模式)失败:', recError);
          wx.showToast({
            title: '启动录音失败，请重试',
            icon: 'none'
          });
          that.setData({
            'isluyin': false,
            'isanzhu': true,
            'textcontent': '按住麦克风录入语音',
            streamStatus: {
              isStreaming: false,
              isRecording: false,
              isConnected: false
            }
          });
        }
      });
    } catch (error) {
      console.error('启动录音失败：', error);
      wx.showToast({
        title: '启动录音失败，请重试',
        icon: 'none'
      });
      that.setData({
        'isluyin': false,
        'isanzhu': true,
        'textcontent': '按住麦克风录入语音',
        streamStatus: {
          isStreaming: false,
          isRecording: false,
          isConnected: false
        }
      });
    }
  },
  // 停止录音（松开按钮）
  stopRecord: function stopRecord() {
    var that = this;
    console.log('停止录音...', {
      timestamp: new Date().toISOString(),
      recorderState: that.data.recorderState
    }); // 先检查是否正在录音

    if (that.recorderManager && that.data.isluyin) {
      try {
        // 只有在录音状态才停止
        if (that.data.recorderState === 'recording') {
          that.recorderManager.stop();
        } else {
          console.log('录音器不在录音状态，无需停止', {
            recorderState: that.data.recorderState
          }); // 即使不在录音状态，也更新UI状态

          that.setData({
            'isluyin': false,
            'isanzhu': true,
            'textcontent': that.data.fullText || '按住麦克风录入语音',
            streamStatus: {
              isStreaming: false,
              isRecording: false,
              isConnected: false
            }
          });
        }
      } catch (error) {
        console.error('停止录音失败：', error);
        that.setData({
          'isluyin': false,
          'isanzhu': true,
          'textcontent': that.data.fullText || '按住麦克风录入语音',
          streamStatus: {
            isStreaming: false,
            isRecording: false,
            isConnected: false
          }
        });
      }
    } else {
      console.log('录音管理器不存在或未开始录音');
      that.setData({
        'isluyin': false,
        'isanzhu': true,
        'textcontent': that.data.fullText || '按住麦克风录入语音',
        streamStatus: {
          isStreaming: false,
          isRecording: false,
          isConnected: false
        }
      });
    }
  },
  // 修改 touchStart 函数，添加更多的保护措施防止连续点击
  touchStart: function touchStart() {
    var that = this;
    console.log('按下录音按钮', {
      timestamp: new Date().toISOString(),
      recorderState: that.data.recorderState,
      hasSocketConnection: !!that.data.socketTaskId,
      isButtonPressed: that.data.isButtonPressed,
      isRecording: that.data.isluyin
    }); // 如果按钮已经处于按下状态或者正在录音中，直接返回

    if (that.data.isButtonPressed || that.data.isluyin) {
      console.log('按钮已经处于按下状态或正在录音中，忽略此次点击');
      return;
    } // 添加一个标记表示正在处理录音启动


    if (that.data.isProcessingRecording) {
      console.log('正在处理上一次录音操作，请稍候再试');
      return;
    } // 设置处理标记


    that.setData({
      isProcessingRecording: true
    }); // 设置按钮按下状态

    that.setData({
      isButtonPressed: true,
      wsConnectionClosed: false
    }); // 如果当前有WebSocket连接，强制关闭它

    if (that.data.socketTaskId) {
      console.log('检测到现有WebSocket连接，强制关闭');
      that.closeWebSocket(true); // 使用强制关闭模式
    } // 等待确保WebSocket已关闭


    setTimeout(function () {
      // 再次检查按钮是否仍然按下，防止用户快速点击
      if (!that.data.isButtonPressed) {
        console.log('按钮已松开，不开始录音');
        that.setData({
          isProcessingRecording: false
        });
        return;
      } // 检查录音状态，确保录音管理器处于空闲状态


      if (that.data.recorderState !== 'idle') {
        console.log('录音管理器不在空闲状态，尝试重置', {
          timestamp: new Date().toISOString(),
          recorderState: that.data.recorderState
        }); // 尝试停止现有录音

        try {
          that.recorderManager.stop();
        } catch (error) {
          console.error('停止现有录音失败:', error);
        } // 重置录音状态


        that.setData({
          recorderState: 'idle'
        }); // 延迟一点开始新录音

        setTimeout(function () {
          if (that.data.isButtonPressed) {
            that.setData({
              isProcessingRecording: false
            });
            that.startRecordingProcess();
          } else {
            that.setData({
              isProcessingRecording: false
            });
          }
        }, 500); // 增加等待时间，确保录音管理器有足够时间重置

        return;
      } // 开始录音流程


      that.startRecordingProcess();
    }, 200); // 增加等待时间，确保WebSocket关闭操作已完成
  },
  // 将录音启动流程提取为单独的函数
  startRecordingProcess: function startRecordingProcess() {
    var that = this; // 清空之前的识别结果

    that.setData({
      isluyin: true,
      isanzhu: false,
      textcontent: '正在录音...',
      currentText: '',
      // 清空当前文本
      fullText: '',
      // 清空完整文本
      audioBuffer: [],
      wsConnectionFailed: false,
      useWebSocket: true,
      // 始终使用WebSocket
      audioDataSent: false,
      // 重置音频数据发送状态
      lastAudioDataTime: 0,
      // 重置最后音频数据时间
      frameReceived: false,
      // 重置帧接收状态
      lastFrameTime: 0,
      // 重置最后帧时间
      streamStatus: {
        isStreaming: true,
        // 设置为true表示正在流式识别
        isRecording: true,
        isConnected: false
      }
    }); // 重置音频处理变量，参考Flask项目

    that.resetAudioDataProcessing(); // 所有设备都使用WebSocket连接

    that.connectWebSocket().then(function () {
      // 再次检查按钮状态，防止在连接过程中按钮被松开
      if (!that.data.isButtonPressed) {
        console.log('WebSocket连接成功，但按钮已松开，不开始录音');
        that.closeWebSocket();
        that.setData({
          isProcessingRecording: false
        });
        return;
      } // 最后一次检查录音状态，确保在idle状态才开始录音


      if (that.data.recorderState !== 'idle') {
        console.log('录音管理器仍不在空闲状态，无法开始新录音', {
          recorderState: that.data.recorderState
        });
        that.setData({
          isButtonPressed: false,
          isluyin: false,
          isanzhu: true,
          textcontent: '按住麦克风录入语音',
          isProcessingRecording: false
        });
        that.closeWebSocket();
        return;
      }

      console.log('WebSocket连接成功，开始录音', {
        timestamp: new Date().toISOString(),
        recorderState: that.data.recorderState
      });

      try {
        // iOS设备使用定时器方式模拟流式传输
        if (that.data.isIOS) {
          that.startIOSWebSocketStreamRecognition();
        } else {
          // 安卓设备使用简化配置，更接近公司demo的设置
          var options = {
            sampleRate: 16000,
            numberOfChannels: 1,
            encodeBitRate: 48000,
            format: 'wav',
            frameSize: 16 // 更改为16，与公司demo保持一致

          };
          that.recorderManager.start(options);
        } // 录音开始后清除处理标记


        that.setData({
          isProcessingRecording: false
        });
      } catch (recError) {
        console.error('开始录音失败:', recError);
        wx.showToast({
          title: '启动录音失败，请重试',
          icon: 'none'
        });
        that.closeWebSocket();
        that.setData({
          isButtonPressed: false,
          isluyin: false,
          isanzhu: true,
          textcontent: '按住麦克风录入语音',
          streamStatus: {
            isStreaming: false,
            isRecording: false,
            isConnected: false
          },
          isProcessingRecording: false
        });
      }
    })["catch"](function (error) {
      console.error('WebSocket连接失败：', error); // WebSocket连接失败时，仍然尝试重新连接WebSocket

      console.log('WebSocket连接失败，将尝试重新连接'); // 重置连接状态但保持WebSocket模式

      that.setData({
        isConnecting: false,
        streamStatus: {
          isStreaming: false,
          isRecording: true,
          isConnected: false
        },
        isProcessingRecording: false
      }); // 尝试重新连接WebSocket

      if (that.data.isButtonPressed) {
        that.reconnectWebSocket();
      }

      wx.showToast({
        title: '连接失败，正在重试',
        icon: 'none'
      });
    });
  },
  // 修改 touchEnd 函数，确保释放资源
  touchEnd: function touchEnd() {
    var that = this;
    console.log('松开录音按钮', {
      timestamp: new Date().toISOString(),
      isIOS: that.data.isIOS,
      recorderState: that.data.recorderState,
      useWebSocket: that.data.useWebSocket,
      isButtonPressed: that.data.isButtonPressed
    }); // 如果按钮未处于按下状态，直接返回

    if (!that.data.isButtonPressed) {
      console.log('按钮未处于按下状态，忽略此次释放');
      return;
    } // 立即设置按钮状态为未按下


    that.setData({
      isButtonPressed: false
    }); // 清除所有相关定时器

    that.clearAllTimers(); // iOS设备特殊处理

    if (that.data.isIOS) {
      console.log('iOS设备结束录音');

      if (that.data.socketTaskId) {
        // 停止录音
        if (that.data.recorderState === 'recording') {
          try {
            that.recorderManager.stop();
          } catch (error) {
            console.error('停止录音失败:', error); // 尝试重置录音状态

            that.setData({
              recorderState: 'idle'
            });
          }
        } // 发送一个最后的音频数据


        if (that.data.audioDataBuffer && that.data.audioDataBuffer.length > 0) {
          var lastBuffer = that.data.audioDataBuffer[that.data.audioDataBuffer.length - 1];

          if (lastBuffer) {
            that.processWebSocketAudioData(lastBuffer, true);
          }
        } // 按照Flask项目方式，发送end标记后关闭WebSocket


        setTimeout(function () {
          that.closeWebSocket(); // 使用修改后的closeWebSocket函数
        }, 200);
      } else if (that.data.iosStreamingConfig.enabled) {
        // 停止iOS流式识别
        that.stopEnhancedIOSStreamRecognition();
      }
    } else {
      // 非iOS设备，使用常规方式停止录音
      if (that.recorderManager) {
        console.log('停止录音', {
          timestamp: new Date().toISOString(),
          useWebSocket: that.data.useWebSocket
        });

        try {
          // 停止录音
          if (that.data.recorderState === 'recording') {
            that.recorderManager.stop();
          } else {
            // 如果不在录音状态，确保重置状态
            that.setData({
              recorderState: 'idle'
            });
          }
        } catch (error) {
          console.error('停止录音失败：', error);
          wx.showToast({
            title: '停止录音失败',
            icon: 'none'
          }); // 尝试重置录音状态

          that.setData({
            recorderState: 'idle'
          });
        }
      } // 关闭WebSocket连接


      that.closeWebSocket();
    } // 设置最终状态


    that.setData({
      isluyin: false,
      isStreaming: false,
      streamStatus: {
        isStreaming: false,
        isRecording: false,
        isConnected: false
      },
      isProcessingRecording: false // 确保处理标记被清除

    });
  },
  // 添加清除所有定时器的辅助函数
  clearAllTimers: function clearAllTimers() {
    var that = this; // 清除帧检测定时器

    if (that.data.frameCheckTimer) {
      clearInterval(that.data.frameCheckTimer);
      that.setData({
        frameCheckTimer: null
      });
    } // 清除iOS WebSocket流式传输定时器


    if (that.data.iosWebSocketTimer) {
      clearInterval(that.data.iosWebSocketTimer);
      that.setData({
        iosWebSocketTimer: null
      });
    } // 清除WebSocket重试定时器


    if (that.data.wsRetryTimer) {
      clearTimeout(that.data.wsRetryTimer);
      that.setData({
        wsRetryTimer: null
      });
    } // 清除WebSocket超时定时器


    if (that.data.wsTimeoutTimer) {
      clearTimeout(that.data.wsTimeoutTimer);
      that.setData({
        wsTimeoutTimer: null
      });
    } // 清除保活定时器


    if (that.data.keepAliveTimer) {
      clearInterval(that.data.keepAliveTimer);
      that.setData({
        keepAliveTimer: null
      });
    } // 清除iOS流式识别相关定时器


    if (that.data.iosStreamingConfig) {
      // 清除分段定时器
      if (that.data.iosStreamingConfig.segmentTimer) {
        clearInterval(that.data.iosStreamingConfig.segmentTimer);
        that.setData({
          'iosStreamingConfig.segmentTimer': null
        });
      } // 清除动画定时器


      if (that.data.iosStreamingConfig.animationTimer) {
        clearInterval(that.data.iosStreamingConfig.animationTimer);
        that.setData({
          'iosStreamingConfig.animationTimer': null
        });
      } // 清除录音超时定时器


      if (that.data.iosStreamingConfig.recordingTimeout) {
        clearTimeout(that.data.iosStreamingConfig.recordingTimeout);
        that.setData({
          'iosStreamingConfig.recordingTimeout': null
        });
      }
    }
  }
}, _defineProperty(_Page, "onLoad", function onLoad() {
  try {
    // 判断设备类型
    var systemInfo = wx.getSystemInfoSync();
    console.log('系统信息:', systemInfo);
    this.setData({
      isIOS: systemInfo.platform === 'ios',
      // 修改iOS的音频配置，与Flask项目普通话流识别保持一致
      iosAudioConfig: {
        sampleRate: 16000,
        // 16kHz采样率，与普通话识别一致
        numberOfChannels: 1,
        // 单声道
        encodeBitRate: 48000,
        // 修改为48kbps，满足微信小程序对iOS的要求（大于24000且小于96000）
        format: 'wav',
        // wav格式
        frameSize: 1 // 启用流式传输

      },
      isProcessingRecording: false // 添加处理录音的标记

    });
    console.log('当前设备类型:', systemInfo.platform); // 初始化录音管理器

    this.initRecorderManager();
  } catch (error) {
    console.error('onLoad初始化失败:', error);
    wx.showToast({
      title: '初始化失败，请重试',
      icon: 'none'
    });
  }
}), _defineProperty(_Page, "initRecorderManager", function initRecorderManager() {
  var that = this; // 如果已经存在录音管理器，先尝试释放

  if (that.recorderManager) {
    try {
      // 尝试停止可能正在进行的录音
      if (that.data.recorderState === 'recording') {
        that.recorderManager.stop();
      } // 解绑事件监听，避免重复监听


      if (that.recorderManager.offStart) that.recorderManager.offStart();
      if (that.recorderManager.offStop) that.recorderManager.offStop();
      if (that.recorderManager.offError) that.recorderManager.offError();
      if (that.recorderManager.offFrameRecorded) that.recorderManager.offFrameRecorded();
      console.log('释放现有录音管理器');
    } catch (error) {
      console.error('释放录音管理器出错:', error);
    }
  } // 创建新的录音管理器


  that.recorderManager = wx.getRecorderManager(); // 添加iOS设备的帧数据检测

  that.setData({
    lastFrameTime: 0,
    frameReceived: false,
    frameCheckTimer: null,
    recorderState: 'idle' // 初始化录音状态

  }); // 添加offStop方法，如果不存在则创建一个空方法

  if (!that.recorderManager.offStop) {
    that.recorderManager.offStop = function () {
      console.log('自定义offStop方法被调用'); // 这是一个空方法，仅用于兼容性
    };
  }

  that.recorderManager.onFrameRecorded(function (res) {
    // 添加日志确认回调触发
    console.log('onFrameRecorded触发', {
      hasData: !!res.frameBuffer,
      dataSize: res.frameBuffer ? res.frameBuffer.byteLength : 0,
      timestamp: new Date().toISOString()
    }); // 标记已收到帧数据

    that.setData({
      frameReceived: true,
      lastFrameTime: Date.now()
    }); // 如果是iOS设备，不处理帧数据

    if (that.data.isIOS) {
      console.log('iOS设备不处理onFrameRecorded回调');
      return;
    }

    if (!that.data.isButtonPressed || !that.data.socketTaskId) return;
    var frameBuffer = res.frameBuffer,
        isLastFrame = res.isLastFrame; // 输出音频帧信息

    console.log('收到音频帧数据:', {
      frameSize: frameBuffer ? frameBuffer.byteLength : 0,
      isLastFrame: isLastFrame,
      timestamp: new Date().toISOString()
    });

    if (!frameBuffer || !frameBuffer.byteLength) {
      console.warn('音频帧数据为空或长度为0');
      return;
    } // 更新音频数据发送状态


    that.setData({
      audioDataSent: true,
      lastAudioDataTime: Date.now()
    }); // 对于安卓设备，直接发送音频数据，不使用processAudioData

    that.data.socketTaskId.send({
      data: frameBuffer,
      success: function success() {
        console.log('发送音频数据成功，数据长度:', frameBuffer.byteLength, '字节');
      },
      fail: function fail(error) {
        console.error('发送音频数据失败：', error);

        if (that.data.isButtonPressed) {
          that.reconnectWebSocket();
        }
      }
    });
  });
  that.recorderManager.onStart(function () {
    console.log('录音开始', {
      timestamp: new Date().toISOString(),
      isIOS: that.data.isIOS,
      config: that.data.isIOS ? that.data.iosAudioConfig : that.data.audioConfig
    }); // 更新全局录音状态

    that.setData({
      recorderState: 'recording'
    });
  });
  that.recorderManager.onStop(function (res) {
    console.log('录音停止', {
      timestamp: new Date().toISOString(),
      duration: res.duration,
      fileSize: res.fileSize,
      tempFilePath: res.tempFilePath ? '有临时文件' : '无临时文件',
      recorderState: that.data.recorderState,
      isIOS: that.data.isIOS,
      isButtonPressed: that.data.isButtonPressed,
      iosStreamingEnabled: that.data.isIOS ? that.data.iosStreamingConfig.enabled : false,
      useWebSocket: that.data.useWebSocket
    }); // 更新全局录音状态

    that.setData({
      recorderState: 'idle'
    }); // 根据不同的模式处理录音结果

    if (that.data.isIOS) {
      if (that.data.useWebSocket && that.data.socketTaskId) {
        // 处理WebSocket流式传输
        that.handleWebSocketRecorderStop(res);
        return;
      } else if (that.data.iosStreamingConfig.enabled && res.tempFilePath) {
        // 处理分段HTTP录音
        var currentSegment = that.data.iosStreamingConfig.currentSegment;
        that.handleRecorderStop(res, currentSegment);
        return;
      }
    } // 如果WebSocket连接失败或未使用WebSocket，则使用HTTP方式处理


    if ((that.data.wsConnectionFailed || !that.data.useWebSocket) && res.tempFilePath && !that.data.isIOS) {
      console.log('使用HTTP方式处理录音文件');
      that.handleHttpRecognition(res.tempFilePath);
    } // 不要在这里修改文本内容，保持最后的识别结果


    that.setData({
      isluyin: false,
      isStreaming: false,
      streamStatus: {
        isStreaming: false,
        isRecording: false,
        isConnected: false
      }
    });
  });
  that.recorderManager.onError(function (error) {
    console.error('录音错误：', error, {
      recorderState: that.data.recorderState,
      isButtonPressed: that.data.isButtonPressed,
      isProcessingRecording: that.data.isProcessingRecording
    }); // 更新全局录音状态

    that.setData({
      recorderState: 'idle',
      isProcessingRecording: false // 确保处理标记被清除

    }); // 如果是由于录音未开始导致的错误，不显示错误提示

    if (error.errMsg && error.errMsg.indexOf('recorder not start') !== -1) {
      console.log('录音未开始，忽略错误');
      return;
    } // 如果是由于录音已开始导致的错误，也不显示错误提示


    if (error.errMsg && error.errMsg.indexOf('recorder already start') !== -1) {
      console.log('录音已经开始，忽略错误');

      try {
        that.recorderManager.stop();
        setTimeout(function () {
          if (that.data.isButtonPressed) {
            that.recorderManager.start(that.data.isIOS ? that.data.iosAudioConfig : that.data.audioConfig);
          }
        }, 300);
      } catch (e) {
        console.error('尝试重新启动录音失败:', e);
      }

      return;
    }

    wx.showToast({
      title: '录音出错，请重试',
      icon: 'none'
    });
    that.closeWebSocket();
    that.setData({
      isluyin: false,
      isanzhu: true,
      textcontent: '按住麦克风录入语音',
      isButtonPressed: false
    });
  });
}), _defineProperty(_Page, "handleHttpRecognition", function handleHttpRecognition(tempFilePath) {
  var that = this;
  var url = "https://mny.talentedsoft.com/dotcasr";
  var formData = {
    "userid": "tc_wx_yun7",
    "token": "wx5678"
  };

  if (that.data.currentClient == "windows") {
    formData["asr_type"] = "minnanyu";
    url = (_readOnlyError("url"), "https://tc.talentedsoft.com/ajax/wechat/dotcasr_proxy");
  } // 显示识别中动画，确保只显示shibie.gif


  that.setData({
    'isshow': false,
    'isshibie': true,
    // 设置为true显示shibie.gif
    'textcontent': '正在识别中...',
    'isluyin': false,
    // 设置为false表示不在录音状态
    'isanzhu': false // 设置为false不显示tipimg.png

  });
  wx.uploadFile({
    url: url,
    filePath: tempFilePath,
    header: {
      "Content-Type": "multipart/form-data"
    },
    name: 'file',
    formData: formData,
    success: function success(res) {
      console.log('HTTP识别结果：', res.data);

      if (res.data) {
        try {
          var data = JSON.parse(res.data);

          if (data.errCode == "0") {
            if (!data.result) {
              that.setData({
                'isanzhu': false,
                // 保持为false
                'isshibie': false,
                'textcontent': '按住麦克风录入语音',
                'isshow': true
              });
              setTimeout(function () {
                that.setData({
                  'isshow': false
                });
              }, 2000);
            } else {
              that.setData({
                'isanzhu': false,
                'isshibie': false,
                'textcontent': data.result,
                'isshow': false
              });
            }
          } else {
            that.setData({
              'isanzhu': false,
              // 保持为false
              'textcontent': '按住麦克风录入语音',
              'isshibie': false,
              'isshow': true
            });
            setTimeout(function () {
              that.setData({
                'isshow': false
              });
            }, 2000);
          }
        } catch (error) {
          console.error('解析识别结果失败：', error);
          that.handleRecognitionError();
        }
      }
    },
    fail: function fail(err) {
      console.error('HTTP识别请求失败：', err);
      that.handleRecognitionError();
    }
  });
}), _defineProperty(_Page, "handleRecognitionError", function handleRecognitionError() {
  var that = this;
  wx.hideLoading();
  wx.showToast({
    title: "识别服务请求异常，请重试",
    icon: "none",
    duration: 2000
  });
  that.setData({
    'isanzhu': false,
    // 设置为false不显示tipimg.png
    'textcontent': '按住麦克风录入语音',
    'isshibie': false,
    'isshow': false
  });
}), _defineProperty(_Page, "startEnhancedIOSStreamRecognition", function startEnhancedIOSStreamRecognition() {
  var that = this;
  console.log('开始增强型iOS流式识别', {
    timestamp: new Date().toISOString()
  }); // 重置流式识别状态

  that.setData({
    'iosStreamingConfig.enabled': true,
    'iosStreamingConfig.tempFilePaths': [],
    'iosStreamingConfig.currentSegment': 0,
    'iosStreamingConfig.isRecording': true,
    'iosStreamingConfig.recognitionText': '',
    'iosStreamingConfig.recognitionQueue': [],
    'iosStreamingConfig.isProcessingQueue': false,
    'iosStreamingConfig.pendingResults': [],
    'iosStreamingConfig.safeToRecord': true,
    'iosStreamingConfig.fullAudioPath': '',
    'iosStreamingConfig.fullRecordingStarted': false,
    'iosStreamingConfig.recognitionErrorCount': 0,
    'iosStreamingConfig.lastResultTime': Date.now(),
    'iosStreamingConfig.currentBufferCount': 0,
    'iosStreamingConfig.processingLock': false,
    'iosStreamingConfig.consecutiveSilenceCount': 0,
    'iosStreamingConfig.currentSegmentDuration': 800,
    // 使用800毫秒作为默认分段时长
    'iosStreamingConfig.recordingDelay': 50,
    // 减少录音间隔延迟，加快识别响应
    'isshibie': true // 显示识别中动画

  }); // 启动进度动画，提供视觉反馈

  if (that.data.iosStreamingConfig.showProgressAnimation) {
    that.startProgressAnimation();
  } // 开始第一段录音


  that.recordNextSegmentEnhanced(); // 设置分段定时器

  var segmentTimer = setInterval(function () {
    // 如果不再录音或按钮已松开，停止定时器
    if (!that.data.iosStreamingConfig.isRecording || !that.data.isButtonPressed) {
      clearInterval(segmentTimer);
      that.setData({
        'iosStreamingConfig.segmentTimer': null
      });
      return;
    } // 如果当前段数已达到最大值，停止录音


    if (that.data.iosStreamingConfig.currentSegment >= that.data.iosStreamingConfig.maxSegments) {
      clearInterval(segmentTimer);
      that.setData({
        'iosStreamingConfig.segmentTimer': null
      });
      that.stopEnhancedIOSStreamRecognition();
      return;
    } // 停止当前段录音
    // 仅当录音状态为recording时停止


    if (that.data.recorderState === 'recording') {
      that.stopCurrentSegmentEnhanced();
    }
  }, that.data.iosStreamingConfig.currentSegmentDuration); // 使用当前分段时长作为定时器间隔

  that.setData({
    'iosStreamingConfig.segmentTimer': segmentTimer
  });
}), _defineProperty(_Page, "startProgressAnimation", function startProgressAnimation() {
  var that = this; // 清除可能存在的旧定时器

  if (that.data.iosStreamingConfig.animationTimer) {
    clearInterval(that.data.iosStreamingConfig.animationTimer);
  } // 初始化动画状态


  that.setData({
    'iosStreamingConfig.animationText': '正在聆听',
    'iosStreamingConfig.animationDots': ''
  }); // 更新显示文本

  that.updateAnimationText(); // 创建动画定时器

  var animationTimer = setInterval(function () {
    // 如果不再录音或按钮已松开，停止动画
    if (!that.data.iosStreamingConfig.isRecording || !that.data.isButtonPressed) {
      clearInterval(animationTimer);
      that.setData({
        'iosStreamingConfig.animationTimer': null
      });
      return;
    } // 更新动画点


    var dots = that.data.iosStreamingConfig.animationDots;

    if (dots.length >= 3) {
      dots = '';
    } else {
      dots += '.';
    }

    that.setData({
      'iosStreamingConfig.animationDots': dots
    }); // 更新显示文本

    that.updateAnimationText();
  }, 500);
  that.setData({
    'iosStreamingConfig.animationTimer': animationTimer
  });
}), _defineProperty(_Page, "updateAnimationText", function updateAnimationText() {
  var that = this;
  var baseText = that.data.iosStreamingConfig.recognitionText || that.data.iosStreamingConfig.animationText;
  var dots = that.data.iosStreamingConfig.animationDots; // 如果有识别文本，则显示识别文本+动画点，否则显示"正在聆听"+动画点

  var displayText = baseText + dots;
  that.setData({
    textcontent: displayText
  });
}), _defineProperty(_Page, "recordNextSegmentEnhanced", function recordNextSegmentEnhanced() {
  var that = this;
  var currentSegment = that.data.iosStreamingConfig.currentSegment; // 检查录音状态，只有在idle状态才开始新录音

  if (that.data.recorderState !== 'idle') {
    console.log('录音管理器不在空闲状态，延迟录制', {
      timestamp: new Date().toISOString(),
      recorderState: that.data.recorderState
    }); // 设置安全延迟，稍后再尝试

    setTimeout(function () {
      if (that.data.isButtonPressed && that.data.iosStreamingConfig.isRecording) {
        that.recordNextSegmentEnhanced();
      }
    }, 100); // 减少延迟时间，加快响应

    return;
  } // 设置安全标志，防止重复开始录音


  that.setData({
    'iosStreamingConfig.safeToRecord': false
  });
  console.log('录制第' + (currentSegment + 1) + '段', {
    timestamp: new Date().toISOString(),
    recorderState: that.data.recorderState,
    segmentDuration: that.data.iosStreamingConfig.currentSegmentDuration
  }); // 如果启用了自适应分段时长，根据识别结果调整分段时长

  if (that.data.iosStreamingConfig.adaptiveSegmentDuration) {
    that.adjustSegmentDuration();
  } // 设置录音参数，与普通话流识别保持一致


  var options = {
    sampleRate: that.data.iosAudioConfig.sampleRate,
    numberOfChannels: that.data.iosAudioConfig.numberOfChannels,
    encodeBitRate: that.data.iosAudioConfig.encodeBitRate,
    format: that.data.iosAudioConfig.format,
    frameSize: that.data.iosAudioConfig.frameSize,
    duration: that.data.iosStreamingConfig.currentSegmentDuration
  };

  try {
    // 开始录音
    that.recorderManager.start(options); // 设置录音超时，确保不会卡在录音状态

    var recordingTimeout = setTimeout(function () {
      console.log('录音超时检查', {
        timestamp: new Date().toISOString(),
        recorderState: that.data.recorderState,
        segment: currentSegment
      }); // 如果仍在录音状态，尝试停止

      if (that.data.recorderState === 'recording') {
        try {
          that.recorderManager.stop();
        } catch (error) {
          console.error('录音超时停止失败：', error);
        }
      } // 恢复安全标志


      that.setData({
        'iosStreamingConfig.safeToRecord': true
      });
    }, that.data.iosStreamingConfig.currentSegmentDuration + 200); // 减少额外时间，加快响应

    that.setData({
      'iosStreamingConfig.recordingTimeout': recordingTimeout
    }); // 延迟恢复安全标志

    setTimeout(function () {
      that.setData({
        'iosStreamingConfig.safeToRecord': true
      });
    }, 100); // 减少延迟时间，加快响应
  } catch (error) {
    console.error('开始段录音失败：', error); // 恢复安全标志

    that.setData({
      'iosStreamingConfig.safeToRecord': true
    }); // 如果按钮仍然按下，尝试下一段

    if (that.data.isButtonPressed && that.data.iosStreamingConfig.isRecording) {
      setTimeout(function () {
        that.recordNextSegmentEnhanced();
      }, 100); // 减少延迟时间，加快响应
    }
  }
}), _defineProperty(_Page, "stopCurrentSegmentEnhanced", function stopCurrentSegmentEnhanced() {
  var that = this; // 如果正在录音，停止当前段

  if (that.data.iosStreamingConfig.isRecording && that.data.recorderState === 'recording') {
    console.log('停止当前段录音', {
      timestamp: new Date().toISOString(),
      segment: that.data.iosStreamingConfig.currentSegment,
      recorderState: that.data.recorderState
    });

    try {
      // 清除录音超时定时器
      if (that.data.iosStreamingConfig.recordingTimeout) {
        clearTimeout(that.data.iosStreamingConfig.recordingTimeout);
      } // 设置状态为正在停止


      that.setData({
        recorderState: 'stopping'
      });
      that.recorderManager.stop();
    } catch (error) {
      console.error('停止段录音失败：', error); // 恢复状态

      that.setData({
        recorderState: 'idle'
      });
    }
  } else {
    console.log('录音未在进行中，无需停止', {
      timestamp: new Date().toISOString(),
      recorderState: that.data.recorderState
    });
  }
}), _defineProperty(_Page, "stopEnhancedIOSStreamRecognition", function stopEnhancedIOSStreamRecognition() {
  var that = this;
  console.log('停止增强型iOS流式识别', {
    timestamp: new Date().toISOString(),
    recorderState: that.data.recorderState
  }); // 清除分段定时器

  if (that.data.iosStreamingConfig.segmentTimer) {
    clearInterval(that.data.iosStreamingConfig.segmentTimer);
    that.setData({
      'iosStreamingConfig.segmentTimer': null
    });
  } // 清除动画定时器


  if (that.data.iosStreamingConfig.animationTimer) {
    clearInterval(that.data.iosStreamingConfig.animationTimer);
    that.setData({
      'iosStreamingConfig.animationTimer': null
    });
  } // 清除录音超时定时器


  if (that.data.iosStreamingConfig.recordingTimeout) {
    clearTimeout(that.data.iosStreamingConfig.recordingTimeout);
  } // 停止当前段录音（如果正在录音）


  if (that.data.recorderState === 'recording') {
    try {
      // 设置状态为正在停止
      that.setData({
        recorderState: 'stopping'
      });
      that.recorderManager.stop();
    } catch (error) {
      console.error('停止最终录音失败：', error); // 恢复状态

      that.setData({
        recorderState: 'idle'
      });
    }
  } // 移除停止第二个录音管理器的逻辑
  // 保留当前识别的文本作为最终结果


  var finalText = that.data.iosStreamingConfig.recognitionText || that.data.textcontent;

  if (finalText && finalText !== '正在录音...' && finalText !== '正在处理...') {
    that.setData({
      textcontent: finalText
    });
  } // 更新状态


  that.setData({
    'iosStreamingConfig.isRecording': false,
    'isshibie': false,
    'iosStreamingConfig.useFullAudioRecognition': false // 关闭完整录音识别，直接使用当前结果

  }); // 清理临时文件

  that.cleanupTempFiles().then(function () {
    console.log('停止识别后临时文件清理完成');
  })["catch"](function (error) {
    console.error('停止识别后清理临时文件失败:', error);
  });
}), _defineProperty(_Page, "handleRecorderStop", function handleRecorderStop(res, currentSegment) {
  var that = this;

  if (that.data.isIOS && that.data.iosStreamingConfig.enabled && res.tempFilePath) {
    console.log('段录音停止回调', {
      timestamp: new Date().toISOString(),
      segment: currentSegment,
      hasFilePath: !!res.tempFilePath,
      isButtonPressed: that.data.isButtonPressed,
      isRecording: that.data.iosStreamingConfig.isRecording,
      duration: res.duration
    }); // 添加到临时文件列表

    var tempFilePaths = that.data.iosStreamingConfig.tempFilePaths;
    tempFilePaths.push(res.tempFilePath); // 增加缓冲区计数

    that.setData({
      'iosStreamingConfig.tempFilePaths': tempFilePaths,
      'iosStreamingConfig.currentBufferCount': that.data.iosStreamingConfig.currentBufferCount + 1
    }); // 发送录音文件进行识别

    that.processAudioSegment(res.tempFilePath, currentSegment); // 如果按钮仍然按下且需要继续录音，开始下一段录音

    if (that.data.isButtonPressed && that.data.iosStreamingConfig.isRecording) {
      // 设置安全延迟，确保录音管理器状态已重置
      setTimeout(function () {
        if (that.data.isButtonPressed && that.data.iosStreamingConfig.isRecording) {
          that.setData({
            'iosStreamingConfig.currentSegment': currentSegment + 1
          }); // 开始下一段录音

          that.recordNextSegmentEnhanced();
        }
      }, that.data.iosStreamingConfig.recordingDelay || 50); // 使用较短的延迟提高响应速度
    }
  }
}), _defineProperty(_Page, "processAudioSegment", function processAudioSegment(filePath, segmentIndex) {
  var that = this;

  if (!filePath) {
    console.error('无效的音频文件路径');
    return;
  }

  console.log('处理音频段', {
    timestamp: new Date().toISOString(),
    segment: segmentIndex,
    filePath: '已提供'
  }); // 发送音频段进行识别，使用与普通话流识别相同的参数

  wx.uploadFile({
    url: "https://mny.talentedsoft.com/dotcasr",
    // 保持URL不变
    filePath: filePath,
    header: {
      "Content-Type": "multipart/form-data"
    },
    name: 'file',
    formData: {
      "userid": "test123",
      // 保持凭证不变
      "token": "test1",
      // 保持凭证不变
      "is_partial": "1",
      // 标记为部分识别
      "segmentIndex": segmentIndex.toString() // 添加段索引

    },
    success: function success(res) {
      // 减少缓冲区计数
      that.setData({
        'iosStreamingConfig.currentBufferCount': Math.max(0, that.data.iosStreamingConfig.currentBufferCount - 1)
      });
      console.log('识别结果返回', {
        timestamp: new Date().toISOString(),
        segment: segmentIndex
      });

      if (res.data) {
        try {
          var data = JSON.parse(res.data);

          if (data.errCode == "0") {
            // 更新上次结果时间
            that.setData({
              'iosStreamingConfig.lastResultTime': Date.now(),
              'iosStreamingConfig.recognitionErrorCount': 0 // 重置错误计数

            }); // 处理识别结果

            that.handleRecognitionResult(data.result, segmentIndex); // 如果启用了静音检测，检查是否为静音

            if (that.data.iosStreamingConfig.silenceDetectionEnabled) {
              if (!data.result || data.result.trim() === '') {
                // 增加连续静音计数
                that.setData({
                  'iosStreamingConfig.consecutiveSilenceCount': that.data.iosStreamingConfig.consecutiveSilenceCount + 1
                });
                console.log('检测到潜在静音', {
                  segment: segmentIndex,
                  silenceCount: that.data.iosStreamingConfig.consecutiveSilenceCount
                });
              } else {
                // 重置连续静音计数
                that.setData({
                  'iosStreamingConfig.consecutiveSilenceCount': 0
                });
              }
            } // 识别完成后删除当前临时文件


            that.deleteFile(filePath);
          } else {
            // 处理错误
            that.handleRecognitionError(data.errCode, segmentIndex); // 即使识别失败也要删除临时文件

            that.deleteFile(filePath);
          }
        } catch (error) {
          console.error('解析识别结果失败：', error);
          that.handleRecognitionError('PARSE_ERROR', segmentIndex); // 出错时也要删除临时文件

          that.deleteFile(filePath);
        }
      }
    },
    fail: function fail(err) {
      console.error('识别请求失败：', err); // 减少缓冲区计数

      that.setData({
        'iosStreamingConfig.currentBufferCount': Math.max(0, that.data.iosStreamingConfig.currentBufferCount - 1)
      });
      that.handleRecognitionError('REQUEST_FAILED', segmentIndex); // 请求失败也要删除临时文件

      that.deleteFile(filePath);
    }
  });
}), _defineProperty(_Page, "handleRecognitionResult", function handleRecognitionResult(result, segmentIndex) {
  var that = this; // 跳过空结果

  if (!result || result.trim() === '') {
    console.log('跳过空识别结果', {
      segment: segmentIndex
    });
    return;
  }

  console.log('处理识别结果', {
    segment: segmentIndex,
    result: result
  }); // 处理特殊字符，参考Flask项目

  result = result.replace(/~/g, "\n");
  result = result.replace(/\|/g, " "); // 如果是第一个段且当前没有识别文本，直接使用结果

  if (segmentIndex === 0 && !that.data.iosStreamingConfig.recognitionText) {
    that.setData({
      'iosStreamingConfig.recognitionText': result,
      fullText: result
    }); // 更新显示文本

    that.updateRecognitionText(result);
    return;
  } // 获取当前识别文本


  var currentText = that.data.iosStreamingConfig.recognitionText;
  var fullText = that.data.fullText || ''; // 如果当前文本为空，直接使用新结果

  if (!currentText) {
    that.setData({
      'iosStreamingConfig.recognitionText': result,
      fullText: result
    }); // 更新显示文本

    that.updateRecognitionText(result);
    return;
  } // 智能合并文本


  var mergedText = that.smartMergeText(currentText, result); // 记录合并前后的文本长度变化

  console.log('文本合并:', {
    beforeLength: currentText.length,
    afterLength: mergedText.length,
    difference: mergedText.length - currentText.length
  }); // 如果是安卓端的合并方式，则拼接到完整文本中

  if (fullText && fullText !== currentText) {
    // 获取新增部分，避免重复
    var addedText = mergedText.substring(currentText.length);

    if (addedText) {
      // 更新完整文本
      var newFullText = fullText; // 检查完整文本末尾和新文本开头是否有重叠

      var overlapIndex = that.findOverlap(fullText, addedText);

      if (overlapIndex > 0) {
        // 有重叠，只添加非重叠部分
        newFullText = fullText + addedText.substring(overlapIndex);
      } else {
        // 无重叠，添加空格再拼接，参考Flask项目的处理方式
        newFullText = fullText + ' ' + addedText;
      } // 记录时间，参考Flask项目


      var date = new Date();
      console.log('分段合并结果 [' + that.dateFormat("YYYY-mm-dd HH:MM", date) + ']: ' + newFullText);
      that.setData({
        fullText: newFullText
      }); // 更新当前段文本

      mergedText = newFullText;
    }
  } else {
    // 直接更新完整文本
    that.setData({
      fullText: mergedText
    });
  } // 仅当合并后的文本与当前文本不同时才更新


  if (mergedText !== currentText) {
    that.setData({
      'iosStreamingConfig.recognitionText': mergedText
    }); // 更新显示文本

    that.updateRecognitionText(mergedText);
  }
}), _defineProperty(_Page, "findOverlap", function findOverlap(str1, str2) {
  if (!str1 || !str2) return 0; // 检查末尾和开头重叠的最大长度

  var maxCheck = Math.min(str1.length, str2.length, 10); // 最多检查10个字符

  for (var i = maxCheck; i > 0; i--) {
    var tail = str1.substring(str1.length - i);
    var head = str2.substring(0, i);

    if (tail === head) {
      return i;
    }
  }

  return 0;
}), _defineProperty(_Page, "updateRecognitionText", function updateRecognitionText(text) {
  var that = this; // 如果正在显示动画，则需要保留动画点

  if (that.data.iosStreamingConfig.showProgressAnimation && that.data.iosStreamingConfig.animationTimer) {
    that.setData({
      'iosStreamingConfig.animationText': text
    }); // 更新动画文本

    that.updateAnimationText();
  } else {
    // 直接更新显示文本
    that.setData({
      textcontent: text
    });
  }
}), _defineProperty(_Page, "handleRecognitionError", function handleRecognitionError(errorCode, segmentIndex) {
  var that = this;
  console.log('识别错误', {
    segment: segmentIndex,
    errorCode: errorCode
  }); // 增加错误计数

  that.setData({
    'iosStreamingConfig.recognitionErrorCount': that.data.iosStreamingConfig.recognitionErrorCount + 1
  }); // 如果错误计数超过最大重试次数，可以考虑降低分段时长

  if (that.data.iosStreamingConfig.recognitionErrorCount >= that.data.iosStreamingConfig.maxErrorRetry) {
    console.log('错误次数过多，调整分段时长'); // 如果启用了自适应分段时长，增加分段时长

    if (that.data.iosStreamingConfig.adaptiveSegmentDuration) {
      that.increaseSegmentDuration();
    } // 重置错误计数


    that.setData({
      'iosStreamingConfig.recognitionErrorCount': 0
    });
  }
}), _defineProperty(_Page, "adjustSegmentDuration", function adjustSegmentDuration() {
  var that = this; // 如果不启用自适应分段时长，直接返回

  if (!that.data.iosStreamingConfig.adaptiveSegmentDuration) {
    return;
  } // 根据不同模式调整分段时长


  if (that.data.iosStreamingConfig.realtimeMode) {
    // 实时模式优先考虑实时性，如果识别正常，尝试减少分段时长
    if (that.data.iosStreamingConfig.recognitionErrorCount === 0) {
      that.decreaseSegmentDuration();
    }
  } else if (that.data.iosStreamingConfig.highQualityMode) {
    // 高质量模式优先考虑识别质量，尝试增加分段时长
    that.increaseSegmentDuration();
  } // 如果有连续静音，可以考虑增加分段时长


  if (that.data.iosStreamingConfig.consecutiveSilenceCount >= that.data.iosStreamingConfig.silenceThreshold) {
    that.increaseSegmentDuration();
  } // 检查识别超时


  var timeSinceLastResult = Date.now() - that.data.iosStreamingConfig.lastResultTime;

  if (timeSinceLastResult > that.data.iosStreamingConfig.resultTimeout) {
    console.log('识别超时，调整分段时长');
    that.increaseSegmentDuration();
  }
}), _defineProperty(_Page, "decreaseSegmentDuration", function decreaseSegmentDuration() {
  var that = this;
  var currentDuration = that.data.iosStreamingConfig.currentSegmentDuration;
  var minDuration = that.data.iosStreamingConfig.minSegmentDuration;
  var step = that.data.iosStreamingConfig.segmentAdjustStep; // 仅当当前时长大于最小时长时才减少

  if (currentDuration > minDuration) {
    var newDuration = Math.max(minDuration, currentDuration - step);
    console.log('减少分段时长', {
      oldDuration: currentDuration,
      newDuration: newDuration
    });
    that.setData({
      'iosStreamingConfig.currentSegmentDuration': newDuration
    });
  }
}), _defineProperty(_Page, "increaseSegmentDuration", function increaseSegmentDuration() {
  var that = this;
  var currentDuration = that.data.iosStreamingConfig.currentSegmentDuration;
  var maxDuration = that.data.iosStreamingConfig.maxSegmentDuration;
  var step = that.data.iosStreamingConfig.segmentAdjustStep; // 仅当当前时长小于最大时长时才增加

  if (currentDuration < maxDuration) {
    var newDuration = Math.min(maxDuration, currentDuration + step);
    console.log('增加分段时长', {
      oldDuration: currentDuration,
      newDuration: newDuration
    });
    that.setData({
      'iosStreamingConfig.currentSegmentDuration': newDuration
    });
  }
}), _defineProperty(_Page, "performFinalRecognition", function performFinalRecognition(filePath) {
  var that = this; // 如果已禁用完整录音识别，直接返回

  if (!that.data.iosStreamingConfig.useFullAudioRecognition) {
    console.log('已禁用完整录音识别，保留流式识别结果');
    return;
  }

  if (!filePath) {
    console.error('无效的录音文件路径');
    return;
  }

  console.log('执行最终识别', {
    timestamp: new Date().toISOString(),
    hasFilePath: !!filePath
  }); // 显示加载状态

  wx.showLoading({
    title: '正在识别...',
    mask: true
  }); // 发送录音文件进行识别，使用与Flask项目相同的接口和参数

  wx.uploadFile({
    url: "https://mny.talentedsoft.com/dotcasr",
    filePath: filePath,
    header: {
      "Content-Type": "multipart/form-data"
    },
    name: 'file',
    formData: {
      "userid": "test123",
      // 与Flask项目保持一致
      "token": "test1",
      // 与Flask项目保持一致
      "is_partial": "0" // 标记为完整识别

    },
    success: function success(res) {
      wx.hideLoading();
      console.log('最终识别结果返回', {
        timestamp: new Date().toISOString()
      });

      if (res.data) {
        try {
          var data = JSON.parse(res.data);

          if (data.errCode == "0" && data.result) {
            // 处理特殊字符，参考Flask项目
            var result = data.result;
            result = result.replace(/~/g, "\n");
            result = result.replace(/\|/g, " "); // 更新最终识别结果

            that.setData({
              'iosStreamingConfig.recognitionText': result,
              fullText: result,
              textcontent: result
            });
            console.log('最终识别结果', {
              result: result
            }); // 添加时间戳，参考Flask项目

            var date = new Date();
            console.log('最终完整识别结果 [' + that.dateFormat("YYYY-mm-dd HH:MM", date) + ']: ' + result);
          } else {
            console.error('最终识别失败', {
              errCode: data.errCode
            }); // 保留当前识别结果

            that.setData({
              textcontent: that.data.iosStreamingConfig.recognitionText || '识别失败，请重试'
            });
          }
        } catch (error) {
          console.error('解析最终识别结果失败：', error); // 保留当前识别结果

          that.setData({
            textcontent: that.data.iosStreamingConfig.recognitionText || '识别失败，请重试'
          });
        }
      }
    },
    fail: function fail(err) {
      wx.hideLoading();
      console.error('最终识别请求失败：', err); // 保留当前识别结果

      that.setData({
        textcontent: that.data.iosStreamingConfig.recognitionText || '识别失败，请重试'
      });
    }
  });
}), _defineProperty(_Page, "smartMergeText", function smartMergeText(oldText, newText) {
  var that = this; // 如果旧文本为空，直接返回新文本

  if (!oldText) return newText; // 如果新文本为空，直接返回旧文本

  if (!newText) return oldText; // 输出调试信息

  console.log('合并文本:', {
    oldText: oldText.length > 20 ? oldText.substring(0, 20) + '...' : oldText,
    newText: newText.length > 20 ? newText.substring(0, 20) + '...' : newText,
    oldLength: oldText.length,
    newLength: newText.length
  }); // 预处理文本，参考Flask项目的处理方式
  // 替换特殊字符

  oldText = oldText.replace(/~/g, "\n").replace(/\|/g, " ");
  newText = newText.replace(/~/g, "\n").replace(/\|/g, " "); // 去除文本两端的空白字符

  oldText = oldText.trim();
  newText = newText.trim(); // 缓存文本用于改进合并效果

  if (that.data.iosStreamingConfig && that.data.iosStreamingConfig.useTextCache) {
    var textCache = that.data.iosStreamingConfig.textCache || [];
    textCache.push(newText);

    if (textCache.length > (that.data.iosStreamingConfig.maxCacheSize || 5)) {
      textCache.shift(); // 移除最旧的文本
    }

    that.setData({
      'iosStreamingConfig.textCache': textCache
    });
  } // 1. 完全包含检查
  // 如果新文本包含旧文本，说明是更完整的结果，直接使用新文本


  if (newText.includes(oldText)) {
    console.log('新文本完全包含旧文本，使用新文本');
    return newText;
  } // 注释掉旧文本包含新文本的检查，按要求直接拼接
  // if (oldText.includes(newText)) {
  //   console.log('旧文本完全包含新文本，保持旧文本');
  //   return oldText;
  // }
  // 2. 精确重叠检测 - 只保留精确匹配
  // 尝试找到精确重叠部分


  var overlapLength = 0;
  var maxOverlap = Math.min(oldText.length, newText.length); // 精确重叠检测：从较长的长度开始匹配

  for (var i = Math.min(maxOverlap, 40); i >= 3; i--) {
    var oldSuffix = oldText.slice(-i);
    var newPrefix = newText.slice(0, i);

    if (oldSuffix === newPrefix) {
      overlapLength = i;
      console.log('找到精确重叠:', {
        length: i,
        overlap: oldSuffix
      });
      break;
    }
  } // 如果找到精确重叠部分，合并文本去除重叠


  if (overlapLength > 0) {
    return oldText + newText.slice(overlapLength);
  } // 3. 如果没有找到任何重叠，简单连接两个文本


  console.log('未找到重叠，使用简单连接'); // 检查是否需要添加空格

  var needSpace = !(oldText.endsWith(' ') || newText.startsWith(' '));
  return oldText + (needSpace ? ' ' : '') + newText;
}), _defineProperty(_Page, "editDistance", function editDistance(s1, s2) {
  s1 = s1.toLowerCase();
  s2 = s2.toLowerCase();
  var costs = new Array();

  for (var i = 0; i <= s1.length; i++) {
    var lastValue = i;

    for (var j = 0; j <= s2.length; j++) {
      if (i == 0) costs[j] = j;else {
        if (j > 0) {
          var newValue = costs[j - 1];
          if (s1.charAt(i - 1) != s2.charAt(j - 1)) newValue = Math.min(Math.min(newValue, lastValue), costs[j]) + 1;
          costs[j - 1] = lastValue;
          lastValue = newValue;
        }
      }
    }

    if (i > 0) costs[s2.length] = lastValue;
  }

  return costs[s2.length];
}), _defineProperty(_Page, "dateFormat", function dateFormat(fmt, date) {
  var ret;
  var opt = {
    "Y+": date.getFullYear().toString(),
    // 年
    "m+": (date.getMonth() + 1).toString(),
    // 月
    "d+": date.getDate().toString(),
    // 日
    "H+": date.getHours().toString(),
    // 时
    "M+": date.getMinutes().toString(),
    // 分
    "S+": date.getSeconds().toString() // 秒

  };

  for (var k in opt) {
    ret = new RegExp("(" + k + ")").exec(fmt);

    if (ret) {
      fmt = fmt.replace(ret[1], ret[1].length == 1 ? opt[k] : opt[k].padStart(ret[1].length, "0"));
    }
  }

  return fmt;
}), _defineProperty(_Page, "resetAudioDataProcessing", function resetAudioDataProcessing() {
  this.setData({
    audioDataTime: 0,
    audioDataCount: 1,
    audioDataEncBusy: 0,
    audioDataNumber: 0,
    audioDataMaxNumber: 0,
    audioDataChunk: null,
    audioDataBuffer: []
  });
}), _defineProperty(_Page, "processAudioData", function processAudioData(frameBuffer, isLastFrame) {
  var that = this;
  var t1 = Date.now(); // 初始化时间和计数器

  if (that.data.audioDataTime == 0) {
    that.setData({
      audioDataTime: t1,
      audioDataEncBusy: 0,
      audioDataNumber: 0,
      audioDataMaxNumber: 0,
      audioDataChunk: null
    });
  } // 控制发送间隔


  if (!isLastFrame && t1 - that.data.audioDataTime < that.data.audioConfig.sendInterval) {
    console.log('发送间隔未到，缓存数据'); // 添加到缓冲区

    that.data.audioDataBuffer.push(frameBuffer);
    return;
  } // 更新时间


  that.setData({
    audioDataTime: t1
  }); // 递增序号

  var number = that.data.audioDataNumber + 1;
  that.setData({
    audioDataNumber: number,
    audioDataMaxNumber: Math.max(that.data.audioDataMaxNumber, number)
  }); // 检查是否有足够数据

  if (!frameBuffer && !isLastFrame && that.data.audioDataBuffer.length === 0) {
    console.log('没有音频数据可处理');
    return;
  } // 获取要发送的数据


  var dataToSend = frameBuffer; // 如果有缓冲数据，合并处理

  if (that.data.audioDataBuffer.length > 0) {
    // 将当前帧添加到缓冲区
    if (frameBuffer) {
      that.data.audioDataBuffer.push(frameBuffer);
    } // 简单合并缓冲区数据（实际应用中可能需要更复杂的处理）
    // 这里简化处理，实际上应该用ArrayBuffer进行合并


    dataToSend = that.data.audioDataBuffer[that.data.audioDataBuffer.length - 1];
    console.log('合并缓冲区数据，当前缓冲区大小:', that.data.audioDataBuffer.length);
  } // 如果没有数据要发送


  if (!dataToSend && !isLastFrame) {
    console.log('没有数据要发送');
    return;
  } // 实时编码队列阻塞处理，参考Flask项目


  if (!isLastFrame) {
    if (that.data.audioDataEncBusy >= 2) {
      console.log("编码队列阻塞，已丢弃一帧");
      return;
    }
  } // 增加编码队列计数


  that.setData({
    audioDataEncBusy: that.data.audioDataEncBusy + 1
  }); // 发送音频数据

  that.sendAudioData(dataToSend, number, isLastFrame); // 清空缓冲区

  that.setData({
    audioDataBuffer: []
  });
}), _defineProperty(_Page, "sendAudioData", function sendAudioData(data, number, isLastFrame) {
  var that = this;

  if (!that.data.socketTaskId) {
    console.log('WebSocket未连接，无法发送数据'); // 减少编码队列计数

    that.setData({
      audioDataEncBusy: Math.max(0, that.data.audioDataEncBusy - 1)
    });
    return;
  }

  if (data) {
    // 发送数据
    that.data.socketTaskId.send({
      data: data,
      success: function success() {
        console.log('发送音频数据成功', {
          number: number,
          dataSize: data ? data.byteLength : 0,
          isLastFrame: isLastFrame,
          count: that.data.audioDataCount
        }); // 增加序号，与Flask项目保持一致

        that.setData({
          audioDataCount: that.data.audioDataCount + 1
        }); // 减少编码队列计数

        that.setData({
          audioDataEncBusy: Math.max(0, that.data.audioDataEncBusy - 1)
        });
      },
      fail: function fail(error) {
        console.error('发送音频数据失败:', error); // 减少编码队列计数

        that.setData({
          audioDataEncBusy: Math.max(0, that.data.audioDataEncBusy - 1)
        });

        if (that.data.isButtonPressed) {
          that.reconnectWebSocket();
        }
      }
    });
  }

  if (isLastFrame) {
    console.log('发送最后一帧数据', {
      number: number
    });
  }
}), _defineProperty(_Page, "startIOSWebSocketStreamRecognition", function startIOSWebSocketStreamRecognition() {
  var that = this;
  console.log('开始iOS设备的WebSocket流式传输', {
    timestamp: new Date().toISOString()
  }); // 配置录音参数，与Flask项目保持一致

  var options = {
    sampleRate: 16000,
    // 16kHz采样率，与Flask项目保持一致
    numberOfChannels: 1,
    // 单声道
    encodeBitRate: 48000,
    // 使用48kbps（微信小程序iOS最低要求）
    format: 'wav',
    // wav格式
    frameSize: 1,
    // 启用流式传输
    duration: 1000 // 分段录音时长，与Flask项目保持一致

  }; // 重置缓冲区和计数器

  that.setData({
    'audioDataBuffer': [],
    'audioDataCount': 1,
    'audioDataTime': Date.now(),
    'audioDataNumber': 0,
    'audioDataMaxNumber': 0,
    'audioDataEncBusy': 0,
    'iosWebSocketTimer': null,
    'wsRecognitionCompleted': false,
    // 重置识别完成标志
    'iosWebSocketConfig': {
      segmentDuration: 1000,
      // 分段时长与Flask项目保持一致
      sendInterval: 100,
      // 发送间隔与Flask项目保持一致，与SendInterval变量一致
      isRecording: true,
      currentSegment: 0,
      maxSegments: 200
    },
    'keepAliveInterval': 3000,
    // 减少心跳间隔为3秒，提高保活频率
    'lastSendTime': Date.now(),
    // 记录最后发送数据的时间
    'lastReceivedTime': Date.now(),
    // 记录最后接收到服务器响应的时间
    'heartbeatTimeout': 10000,
    // 10秒无响应判定为连接异常
    'heartbeatMisses': 0,
    // 记录连续心跳无响应次数
    'maxHeartbeatMisses': 3 // 最大允许的连续心跳无响应次数

  }); // 创建心跳数据包，符合服务器规范的格式

  var createHeartbeatPacket = function createHeartbeatPacket() {
    // 使用与Flask项目兼容的心跳包格式
    // 通常空字符串"ping"或包含特定标记的JSON更容易被服务器识别
    return "ping";
  }; // 添加心跳响应检测


  var checkHeartbeatResponse = function checkHeartbeatResponse() {
    var now = Date.now(); // 如果超过心跳超时时间没有收到响应

    if (now - that.data.lastReceivedTime > that.data.heartbeatTimeout) {
      // 增加心跳失败计数
      that.setData({
        heartbeatMisses: that.data.heartbeatMisses + 1
      });
      console.log('心跳响应超时', {
        misses: that.data.heartbeatMisses,
        maxMisses: that.data.maxHeartbeatMisses,
        timeSinceLastResponse: now - that.data.lastReceivedTime
      }); // 如果连续超过最大失败次数，认为连接已断开

      if (that.data.heartbeatMisses >= that.data.maxHeartbeatMisses) {
        console.log('连续心跳失败次数过多，尝试重连WebSocket'); // 关闭旧连接

        that.closeWebSocket(true); // 尝试重连

        if (that.data.isButtonPressed) {
          setTimeout(function () {
            // 确保按钮仍然按下
            if (that.data.isButtonPressed) {
              console.log('尝试重新连接WebSocket');
              that.connectWebSocket()["catch"](function (error) {
                console.error('WebSocket重连失败:', error); // 切换到HTTP模式

                that.startEnhancedIOSStreamRecognition();
              });
            }
          }, 500);
        }

        return;
      }
    }
  }; // 添加保活定时器，确保WebSocket连接不会因为静默而断开


  var keepAliveTimer = setInterval(function () {
    // 如果按钮已松开或WebSocket未连接，停止定时器
    if (!that.data.isButtonPressed || !that.data.socketTaskId || that.data.wsConnectionClosed) {
      clearInterval(keepAliveTimer);
      that.setData({
        keepAliveTimer: null
      });
      return;
    } // 检查心跳响应


    checkHeartbeatResponse(); // 检查是否超过心跳间隔没有发送数据

    var now = Date.now();

    if (now - that.data.lastSendTime > that.data.keepAliveInterval) {
      console.log('发送心跳包保持连接'); // 发送心跳包

      if (that.data.socketTaskId) {
        that.data.socketTaskId.send({
          data: createHeartbeatPacket(),
          success: function success() {
            that.setData({
              lastSendTime: now
            });
          },
          fail: function fail(error) {
            console.error('发送心跳包失败:', error); // 增加心跳失败计数

            that.setData({
              heartbeatMisses: that.data.heartbeatMisses + 1
            }); // 连续失败超过阈值，认为连接已断开

            if (that.data.heartbeatMisses >= that.data.maxHeartbeatMisses) {
              console.log('心跳发送失败次数过多，尝试重连'); // 尝试重连

              if (that.data.isButtonPressed) {
                that.reconnectWebSocket();
              }
            }
          }
        });
      }
    }
  }, 3000); // 每3秒检查一次，增加检查频率
  // 开始录音

  try {
    that.recorderManager.start(options); // 设置定时器来定期停止和重新开始录音，模拟流式传输

    var streamTimer = setInterval(function () {
      // 如果不再录音或按钮已松开，停止定时器
      if (!that.data.isButtonPressed) {
        clearInterval(streamTimer);
        clearInterval(keepAliveTimer); // 同时清除保活定时器

        that.setData({
          'iosWebSocketTimer': null
        }); // 停止当前录音

        if (that.data.recorderState === 'recording') {
          try {
            that.recorderManager.stop();
          } catch (error) {
            console.error('停止录音失败：', error);
          }
        }

        return;
      } // 如果当前正在录音，停止当前段录音


      if (that.data.recorderState === 'recording') {
        try {
          console.log('WebSocket流式传输：停止当前段录音', {
            timestamp: new Date().toISOString(),
            segment: that.data.iosWebSocketConfig.currentSegment,
            wsRecognitionCompleted: that.data.wsRecognitionCompleted
          }); // 设置状态为正在停止

          that.setData({
            recorderState: 'stopping',
            'iosWebSocketConfig.stoppingSegment': that.data.iosWebSocketConfig.currentSegment
          });
          that.recorderManager.stop();
        } catch (error) {
          console.error('停止段录音失败：', error);
        }
      }
    }, options.duration); // 使用与分段时长相同的定时器间隔

    that.setData({
      'iosWebSocketTimer': streamTimer,
      'keepAliveTimer': keepAliveTimer
    });
  } catch (error) {
    console.error('开始录音失败：', error);
    clearInterval(keepAliveTimer); // 如果WebSocket流式传输失败，回退到分段HTTP方式

    that.startEnhancedIOSStreamRecognition();
  }
}), _defineProperty(_Page, "handleWebSocketRecorderStop", function handleWebSocketRecorderStop(res) {
  var that = this; // 检查按钮状态和 WebSocket 连接状态

  if (!that.data.isButtonPressed || !that.data.socketTaskId) {
    return;
  }

  console.log('WebSocket流式传输：录音段停止回调', {
    timestamp: new Date().toISOString(),
    segment: that.data.iosWebSocketConfig ? that.data.iosWebSocketConfig.stoppingSegment : -1,
    hasFilePath: !!res.tempFilePath,
    duration: res.duration,
    wsRecognitionCompleted: that.data.wsRecognitionCompleted
  }); // 重置 wsRecognitionCompleted 状态，确保不会因为中间结果而停止识别

  if (that.data.wsRecognitionCompleted && that.data.isButtonPressed) {
    that.setData({
      wsRecognitionCompleted: false
    });
  }

  if (res.tempFilePath) {
    // 读取录音文件为ArrayBuffer
    wx.getFileSystemManager().readFile({
      filePath: res.tempFilePath,
      success: function success(readRes) {
        // 获取二进制数据
        var buffer = readRes.data; // 处理音频数据并通过WebSocket发送

        that.processWebSocketAudioData(buffer, false); // 准备下一段录音

        that.setData({
          'iosWebSocketConfig.currentSegment': that.data.iosWebSocketConfig.currentSegment + 1,
          recorderState: 'idle',
          lastSendTime: Date.now() // 更新最后发送数据的时间

        }); // 如果按钮仍然按下，开始下一段录音，无论识别状态如何

        if (that.data.isButtonPressed) {
          try {
            // 配置录音参数
            var options = {
              sampleRate: 16000,
              numberOfChannels: 1,
              encodeBitRate: 48000,
              format: 'wav',
              frameSize: 1,
              duration: that.data.iosWebSocketConfig.segmentDuration
            }; // 延迟一点开始下一段录音，确保上一段处理完毕

            setTimeout(function () {
              if (that.data.isButtonPressed) {
                that.recorderManager.start(options);
              }
            }, 50);
          } catch (error) {
            console.error('开始下一段录音失败：', error);
          }
        }
      },
      fail: function fail(error) {
        console.error('读取录音文件失败：', error); // 准备下一段录音

        that.setData({
          'iosWebSocketConfig.currentSegment': that.data.iosWebSocketConfig.currentSegment + 1,
          recorderState: 'idle'
        }); // 如果按钮仍然按下，开始下一段录音，无论识别状态如何

        if (that.data.isButtonPressed) {
          try {
            // 配置录音参数
            var options = {
              sampleRate: 16000,
              numberOfChannels: 1,
              encodeBitRate: 48000,
              format: 'wav',
              frameSize: 1,
              duration: that.data.iosWebSocketConfig.segmentDuration
            }; // 延迟一点开始下一段录音

            setTimeout(function () {
              if (that.data.isButtonPressed) {
                that.recorderManager.start(options);
              }
            }, 50);
          } catch (error) {
            console.error('开始下一段录音失败：', error);
          }
        }
      }
    });
  } else {
    // 即使没有文件路径，也发送一个空的数据包来保持连接活跃
    if (that.data.socketTaskId) {
      that.data.socketTaskId.send({
        data: new ArrayBuffer(2),
        success: function success() {
          console.log('发送空数据包以保持连接');
          that.setData({
            lastSendTime: Date.now()
          });
        }
      });
    } // 准备下一段录音


    that.setData({
      'iosWebSocketConfig.currentSegment': that.data.iosWebSocketConfig.currentSegment + 1,
      recorderState: 'idle'
    }); // 如果按钮仍然按下，开始下一段录音，无论识别状态如何

    if (that.data.isButtonPressed) {
      try {
        // 配置录音参数
        var options = {
          sampleRate: 16000,
          numberOfChannels: 1,
          encodeBitRate: 48000,
          format: 'wav',
          frameSize: 1,
          duration: that.data.iosWebSocketConfig.segmentDuration
        }; // 延迟一点开始下一段录音

        setTimeout(function () {
          if (that.data.isButtonPressed) {
            that.recorderManager.start(options);
          }
        }, 50);
      } catch (error) {
        console.error('开始下一段录音失败：', error);
      }
    }
  }
}), _defineProperty(_Page, "processWebSocketAudioData", function processWebSocketAudioData(buffer, isLastFrame) {
  var that = this; // 参照Flask项目的RealTimeSendTry函数处理

  var t1 = Date.now(); // 初始化时间和计数器

  if (that.data.audioDataTime == 0) {
    that.setData({
      audioDataTime: t1,
      audioDataEncBusy: 0,
      audioDataNumber: 0,
      audioDataMaxNumber: 0,
      audioDataChunk: null
    });
  } // 控制发送间隔，与Flask项目保持一致


  if (!isLastFrame && t1 - that.data.audioDataTime < (that.data.iosWebSocketConfig.sendInterval || 100)) {
    console.log('发送间隔未到，缓存数据'); // 添加到缓冲区

    that.data.audioDataBuffer.push(buffer);
    return;
  } // 更新时间


  that.setData({
    audioDataTime: t1,
    lastSendTime: t1 // 更新最后发送数据的时间

  }); // 递增序号，与Flask项目一致

  var number = that.data.audioDataNumber + 1;
  that.setData({
    audioDataNumber: number,
    audioDataMaxNumber: Math.max(that.data.audioDataMaxNumber, number),
    audioDataCount: that.data.audioDataCount + 1 // 增加计数器，与Flask项目一致

  }); // 检查是否有足够数据

  if (!buffer && !isLastFrame && that.data.audioDataBuffer.length === 0) {
    console.log('没有音频数据可处理');
    return;
  } // 获取要发送的数据


  var dataToSend = buffer; // 如果有缓冲数据，合并处理

  if (that.data.audioDataBuffer.length > 0) {
    // 将当前帧添加到缓冲区
    if (buffer) {
      that.data.audioDataBuffer.push(buffer);
    } // 取最后一帧作为发送数据（简化处理，实际上Flask项目可能有更复杂的合并逻辑）


    dataToSend = that.data.audioDataBuffer[that.data.audioDataBuffer.length - 1];
    console.log('合并缓冲区数据，当前缓冲区大小:', that.data.audioDataBuffer.length);
  } // 如果没有数据要发送且不是最后一帧


  if (!dataToSend && !isLastFrame) {
    console.log('没有数据要发送');
    return;
  } // 实时编码队列阻塞处理，参考Flask项目


  if (!isLastFrame) {
    if (that.data.audioDataEncBusy >= 2) {
      console.log("编码队列阻塞，已丢弃一帧");
      return;
    }
  } // 增加编码队列计数


  that.setData({
    audioDataEncBusy: that.data.audioDataEncBusy + 1
  }); // 发送音频数据

  if (dataToSend) {
    that.data.socketTaskId.send({
      data: dataToSend,
      success: function success() {
        console.log('发送音频数据成功', {
          number: number,
          dataSize: dataToSend ? dataToSend.byteLength : 0,
          isLastFrame: isLastFrame
        }); // 减少编码队列计数

        that.setData({
          audioDataEncBusy: Math.max(0, that.data.audioDataEncBusy - 1)
        });
      },
      fail: function fail(error) {
        console.error('发送音频数据失败:', error); // 减少编码队列计数

        that.setData({
          audioDataEncBusy: Math.max(0, that.data.audioDataEncBusy - 1)
        });

        if (that.data.isButtonPressed) {
          that.reconnectWebSocket();
        }
      }
    });
  } // 清空缓冲区


  that.setData({
    audioDataBuffer: []
  });

  if (isLastFrame) {
    console.log('发送最后一帧数据', {
      number: number
    });
  }
}), _defineProperty(_Page, "reconnectWebSocket", function reconnectWebSocket() {
  var that = this;

  if (that.data.reconnectCount >= that.data.maxReconnectCount) {
    console.log('WebSocket重连次数已达上限', {
      reconnectCount: that.data.reconnectCount,
      maxCount: that.data.maxReconnectCount,
      isIOS: that.data.isIOS
    }); // 重置连接状态但保持WebSocket模式

    that.setData({
      wsConnectionFailed: true,
      isConnecting: false,
      isStreaming: false,
      // 不再重置重连计数，防止无限循环重连
      // reconnectCount: 0, 
      streamStatus: {
        isStreaming: false,
        isRecording: that.data.streamStatus.isRecording,
        isConnected: false
      }
    }); // iOS设备特殊处理

    if (that.data.isIOS) {
      // 显示更友好的提示
      wx.showToast({
        title: '语音连接不稳定，请重试',
        icon: 'none',
        duration: 2000
      }); // 清除可能存在的重试定时器

      if (that.data.wsRetryTimer) {
        clearTimeout(that.data.wsRetryTimer);
        that.setData({
          wsRetryTimer: null
        });
      }
    } else {
      // 安卓设备显示提示
      wx.showToast({
        title: '连接失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }

    return;
  } // 增加重连计数


  that.setData({
    reconnectCount: that.data.reconnectCount + 1,
    isConnecting: true
  });
  console.log('尝试重连WebSocket', {
    reconnectCount: that.data.reconnectCount,
    timestamp: new Date().toISOString(),
    isIOS: that.data.isIOS
  }); // 设置重连延迟，iOS设备使用递增延迟策略

  var retryDelay = that.data.isIOS ? 1000 * Math.min(2, that.data.reconnectCount) : // iOS设备：首次1秒，之后2秒
  1000; // 安卓设备：固定1秒

  var retryTimer = setTimeout(function () {
    // 如果按钮仍然按下，尝试重连
    if (that.data.isButtonPressed) {
      that.connectWebSocket()["catch"](function (error) {
        console.error('WebSocket重连失败:', error); // 如果按钮仍然按下，继续尝试重连

        if (that.data.isButtonPressed) {
          // 检查是否已达到最大重连次数
          if (that.data.reconnectCount < that.data.maxReconnectCount) {
            that.reconnectWebSocket();
          } else {
            // 已达到最大重连次数，不再继续重连
            console.log('已达到最大重连次数，停止重连'); // iOS设备特殊处理

            if (that.data.isIOS) {
              wx.showToast({
                title: '连接不稳定，请稍后再试',
                icon: 'none',
                duration: 2000
              });
            }
          }
        }
      });
    }
  }, retryDelay);
  that.setData({
    wsRetryTimer: retryTimer
  });
}), _defineProperty(_Page, "cleanupTempFiles", function cleanupTempFiles() {
  var that = this; // 检查是否有临时文件需要清理

  if (!that.data.iosStreamingConfig || !that.data.iosStreamingConfig.tempFilePaths || that.data.iosStreamingConfig.tempFilePaths.length === 0) {
    console.log('没有临时文件需要清理');
    return Promise.resolve();
  }

  console.log('开始清理临时文件', {
    fileCount: that.data.iosStreamingConfig.tempFilePaths.length
  });
  var fs = wx.getFileSystemManager();

  var tempFiles = _toConsumableArray(that.data.iosStreamingConfig.tempFilePaths); // 复制数组以避免迭代过程中修改
  // 创建一个Promise数组来跟踪所有删除操作


  var deletePromises = tempFiles.map(function (filePath) {
    return new Promise(function (resolve) {
      // 检查文件是否存在
      fs.access({
        path: filePath,
        success: function success() {
          // 文件存在，尝试删除
          fs.unlink({
            filePath: filePath,
            success: function success() {
              console.log('成功删除临时文件:', filePath);
              resolve(true);
            },
            fail: function fail(error) {
              console.error('删除临时文件失败:', filePath, error);
              resolve(false);
            }
          });
        },
        fail: function fail() {
          // 文件不存在，视为已成功清理
          console.log('临时文件不存在，无需删除:', filePath);
          resolve(true);
        }
      });
    });
  }); // 等待所有删除操作完成

  return Promise.all(deletePromises).then(function () {
    // 清空临时文件路径数组
    that.setData({
      'iosStreamingConfig.tempFilePaths': []
    });
    console.log('临时文件清理完成');
  })["catch"](function (error) {
    console.error('临时文件清理过程中出错:', error);
  });
}), _defineProperty(_Page, "deleteFile", function deleteFile(filePath) {
  var _this = this;

  if (!filePath) {
    console.log('文件路径为空，无法删除');
    return;
  }

  console.log('尝试删除临时文件:', filePath);
  var fs = wx.getFileSystemManager();
  fs.unlink({
    filePath: filePath,
    success: function success() {
      console.log('成功删除临时文件:', filePath); // 从tempFilePaths数组中移除已删除的文件路径

      if (_this.data.iosStreamingConfig && _this.data.iosStreamingConfig.tempFilePaths) {
        var index = _this.data.iosStreamingConfig.tempFilePaths.indexOf(filePath);

        if (index !== -1) {
          // 创建新数组并移除已删除的文件路径
          var newTempFilePaths = _toConsumableArray(_this.data.iosStreamingConfig.tempFilePaths);

          newTempFilePaths.splice(index, 1); // 更新状态

          _this.setData({
            'iosStreamingConfig.tempFilePaths': newTempFilePaths
          });
        }
      }
    },
    fail: function fail(error) {
      console.error('删除临时文件失败:', error);
    }
  });
}), _defineProperty(_Page, "startKeepAlive", function startKeepAlive() {
  var that = this; // 清除可能存在的旧定时器

  if (that.data.keepAliveTimer) {
    clearInterval(that.data.keepAliveTimer);
  } // 设置保活间隔为3秒


  var keepAliveTimer = setInterval(function () {
    // 如果按钮已松开或WebSocket未连接，停止定时器
    if (!that.data.isButtonPressed || !that.data.socketTaskId || that.data.wsConnectionClosed) {
      clearInterval(keepAliveTimer);
      return;
    } // 发送心跳包


    try {
      that.data.socketTaskId.send({
        data: "ping",
        success: function success() {
          console.log('发送心跳包成功');
          that.setData({
            lastSendTime: Date.now()
          });
        },
        fail: function fail(error) {
          console.error('发送心跳包失败:', error); // 心跳失败，可能需要重连

          that.handleHeartbeatFailure();
        }
      });
    } catch (error) {
      console.error('发送心跳包异常:', error);
      that.handleHeartbeatFailure();
    }
  }, 3000);
  that.setData({
    keepAliveTimer: keepAliveTimer
  });
}), _defineProperty(_Page, "handleHeartbeatFailure", function handleHeartbeatFailure() {
  var that = this; // 增加心跳失败计数

  that.setData({
    heartbeatMisses: that.data.heartbeatMisses + 1
  }); // 如果连续失败超过阈值，尝试重连

  if (that.data.heartbeatMisses >= 3 && that.data.isButtonPressed) {
    console.log('心跳连续失败，尝试重连WebSocket');
    that.reconnectWebSocket();
  }
}), _defineProperty(_Page, "touchCancel", function touchCancel() {
  console.log('触摸取消，停止录音'); // 触摸取消时执行与触摸结束相同的逻辑

  this.touchEnd();
}), _Page));