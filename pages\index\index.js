// pages / ruyin / ruyin.js
//获取应用实例
const app = getApp()
var common = require("./../../dist/js/common.js")

// 优化：定义常量，消除魔法数字和硬编码字符串
const MESSAGES = {
  INITIAL: '点击麦克风开始录音',
  RECORDING: '正在录音中，点击结束录音',
  PROCESSING: '正在处理识别结果...',
  RECOGNIZING: '正在识别中...',
  RECORDING_OLD: '正在录音...',
  FAILED: '识别失败，请重试'
};

const DELAYS = {
  WEBSOCKET_CLOSE: 200,
  RESULT_CHECK: 500,
  ERROR_DISPLAY: 2000,
  RECORDING_RESET: 200, // 减少录音重置延迟
  HEALTH_CHECK: 3000,
  RECORDING_RESTART: 100, // 减少录音重启延迟
  HEALTH_CHECK_INTERVAL: 1000,
  MINIMAL_CLEANUP: 50 // 新增：最小清理延迟
};

Page({
  data: {
    isshibie: false,
    isluyin: false,
    isshow: false,
    isanzhu: true,
    textcontent: MESSAGES.INITIAL,
    historyResults: [], // 识别历史记录
    historyScrollTop: 0, // 历史记录滚动位置，用于自动滚动到底部

    // 智能录音超时相关状态
    intelligentStopTimer: null, // 智能停止定时器
    noResultTimeout: 60000, // 无识别结果超时时间（60秒）

    socketTaskId: null,
    isConnected: false,
    isConnecting: false, // 是否正在连接中
    wsConnectionFailed: false, // 是否已经尝试过WebSocket连接并失败
    currentText: "", // 当前识别的文本
    fullText: "", // 完整的识别文本
    lastIntermediateResult: "", // 最后一次中间识别结果
    // 统一的录音配置（iOS和Android完全一致）
    audioConfig: {
      sampleRate: 16000, // 统一采样率16kHz
      numberOfChannels: 1, // 统一单声道
      encodeBitRate: 64000, // 统一比特率64kbps
      format: 'pcm', // 统一使用pcm格式
      frameSize: 2, // 统一帧大小2KB
      duration: 600000 // 增加到10分钟，由智能逻辑控制实际录音时长
    },
    audioBuffer: [],
    lastSendTime: 0,
    streamStatus: {
      isStreaming: false,
      isRecording: false,
      isConnected: false,
    },
    keepAliveTimer: null,
    isIOS: false,
    frameCheckTimer: null,
    // 优化：lastFrameTime和frameReceived改为内存变量，减少setData调用
    recorderState: 'idle',
    wsConnectionClosed: false,
    isProcessingRecording: false,

    // 新增：页面状态管理相关变量
    pageHideTime: 0, // 页面隐藏时间戳
    isPageVisible: true, // 页面是否可见
    backgroundInterrupted: false, // 是否因后台中断
    recordingStartTime: 0, // 录音开始时间戳
    keepScreenOnEnabled: false, // 屏幕常亮状态标记

    //   PC端标识
    currentClient: "mobile" // 默认移动端，onLoad时会根据平台检测更新
  },

  // 优化：提取公共状态设置函数，减少重复代码
  resetToInitialState: function (additionalData = {}) {
    // 🔆 重置状态时关闭屏幕常亮
    this.disableKeepScreenOn();

    const baseState = {
      textcontent: MESSAGES.INITIAL,
      isanzhu: true,
      isshibie: false,
      isshow: false,
      isluyin: false, // 确保录音状态被重置
      recorderState: 'idle', // 确保录音器状态被重置
      isProcessingRecording: false, // 确保处理状态被重置
      currentText: '',
      lastIntermediateResult: '',
      backgroundInterrupted: false, // 重置后台中断状态
      recordingStartTime: 0 // 重置录音开始时间
    };
    this.setData({
      ...baseState,
      ...additionalData
    });
  },

  resetToErrorState: function (additionalData = {}) {
    const errorState = {
      isanzhu: false,
      isshibie: false,
      textcontent: MESSAGES.INITIAL,
      isshow: true
    };
    this.setData({
      ...errorState,
      ...additionalData
    });
  },

  resetConnectionState: function () {
    return {
      socketTaskId: null,
      isConnected: false,
      isConnecting: false,
      streamStatus: {
        isStreaming: false,
        isRecording: false,
        isConnected: false
      }
    };
  },

  // 获取PC端优化的录音配置
  getPcAudioConfig: function () {
    return {
      sampleRate: 16000,
      numberOfChannels: 1,
      format: 'wav',
      frameSize: 16,
      duration: 600000
    };
  },

  // 优化：简化复杂的文本验证逻辑
  isValidRecognitionText: function (text) {
    if (!text || text.trim() === '') return false;

    const systemMessages = [
      MESSAGES.INITIAL,
      MESSAGES.RECORDING,
      MESSAGES.PROCESSING,
      MESSAGES.RECOGNIZING,
      MESSAGES.RECORDING_OLD,
      MESSAGES.FAILED
    ];

    return !systemMessages.includes(text);
  },

  // 优化：统一的状态更新函数，减少setData调用频次
  updateState: function (updates) {
    // 批量更新状态，减少页面渲染触发
    if (Object.keys(updates).length > 0) {
      this.setData(updates);
    }
  },

  // 优化：批量更新连接状态
  updateConnectionState: function (connectionUpdates) {
    const updates = {};

    if (connectionUpdates.isConnected !== undefined) {
      updates.isConnected = connectionUpdates.isConnected;
      updates['streamStatus.isConnected'] = connectionUpdates.isConnected;
    }

    if (connectionUpdates.isConnecting !== undefined) {
      updates.isConnecting = connectionUpdates.isConnecting;
    }

    if (connectionUpdates.socketTaskId !== undefined) {
      updates.socketTaskId = connectionUpdates.socketTaskId;
    }

    this.updateState(updates);
  },

  // 新增：专门的滚动到底部方法，确保历史记录滚动可靠
  scrollHistoryToBottom: function () {
    const that = this;

    // 使用多重策略确保滚动到底部
    // 策略1：立即设置大数值滚动位置
    that.setData({
      historyScrollTop: 999999
    });

    // 策略2：延迟再次确保滚动到底部（防止DOM更新延迟）
    setTimeout(() => {
      that.setData({
        historyScrollTop: 999999 + Date.now() % 1000 // 添加随机数确保触发更新
      });
    }, 100);

    console.log('🔄 执行滚动到底部操作');
  },

  // 历史记录滚动事件监听，用于调试滚动行为
  onHistoryScroll: function (e) {
    const {
      scrollTop,
      scrollHeight,
      scrollWidth
    } = e.detail;
    console.log('📜 历史记录滚动事件:', {
      scrollTop: scrollTop,
      scrollHeight: scrollHeight,
      scrollWidth: scrollWidth,
      recordCount: this.data.historyResults.length
    });
  },

  // 智能录音超时管理：基于识别结果的自动停止逻辑
  startIntelligentStopTimer: function () {
    const that = this;

    // 清除现有的定时器
    if (that.data.intelligentStopTimer) {
      clearTimeout(that.data.intelligentStopTimer);
    }

    // 设置新的智能停止定时器
    const timer = setTimeout(() => {
      console.log('🤖 智能停止：60秒内无识别结果，自动停止录音', {
        isPageVisible: that.data.isPageVisible,
        isRecording: that.data.isluyin
      });
      // 无论页面是否可见，都执行停止录音操作
      that.stopRecording();
    }, that.data.noResultTimeout);

    that.setData({
      intelligentStopTimer: timer
    });

    console.log('   启动智能录音超时定时器，60秒内无识别结果将自动停止');
  },

  // 重置智能停止定时器：当有新的识别结果时调用
  resetIntelligentStopTimer: function () {
    const that = this;

    // 清除现有定时器
    if (that.data.intelligentStopTimer) {
      clearTimeout(that.data.intelligentStopTimer);
    }

    // 重新启动定时器
    const timer = setTimeout(() => {
      console.log('🤖 智能停止：重置后60秒内无新识别结果，自动停止录音', {
        isPageVisible: that.data.isPageVisible,
        isRecording: that.data.isluyin
      });
      // 无论页面是否可见，都执行停止录音操作
      that.stopRecording();
    }, that.data.noResultTimeout);

    that.setData({
      intelligentStopTimer: timer
    });

    console.log('🔄 重置智能录音超时定时器，检测到识别结果');
  },

  // 清除智能停止定时器
  clearIntelligentStopTimer: function () {
    const that = this;

    if (that.data.intelligentStopTimer) {
      clearTimeout(that.data.intelligentStopTimer);
      that.setData({
        intelligentStopTimer: null
      });
      console.log('   清除智能录音超时定时器');
    }
  },

  // 流式添加识别结果到历史记录（每个识别片段）
  addToHistory: function (text, serverTimestamp = null) {
    // 优化：使用简化的验证逻辑
    if (!this.isValidRecognitionText(text)) {
      return;
    }

    // 使用服务端提供的时间戳(如果有)或客户端时间，只保留时分秒
    const now = new Date();

    // 如果有服务端时间戳，只提取时分秒部分
    let timeString;
    if (serverTimestamp) {
      // 尝试从服务端时间戳中提取时分秒
      const timeMatch = serverTimestamp.match(/(\d{2}:\d{2}:\d{2})/);
      timeString = timeMatch ? timeMatch[1] : (
        now.getHours().toString().padStart(2, '0') + ':' +
        now.getMinutes().toString().padStart(2, '0') + ':' +
        now.getSeconds().toString().padStart(2, '0')
      );
    } else {
      // 使用客户端时间的时分秒
      timeString = now.getHours().toString().padStart(2, '0') + ':' +
        now.getMinutes().toString().padStart(2, '0') + ':' +
        now.getSeconds().toString().padStart(2, '0');
    }

    const historyItem = {
      text: text.trim(),
      time: timeString,
      fromServer: !!serverTimestamp // 标记是否来自服务器时间戳
    };

    // 获取当前历史记录
    let currentHistory = this.data.historyResults || [];

    // 检查是否与最新一条记录重复（避免重复添加相同内容）
    // 修改：检查数组末尾的记录（因为最新记录现在在末尾）
    if (currentHistory.length > 0 && currentHistory[currentHistory.length - 1].text === historyItem.text) {
      return;
    }

    // 修改：添加到历史记录末尾（最新的在下面，与Flask项目保持一致）
    currentHistory.push(historyItem);

    // 限制历史记录数量，最多保留30条（流式识别会产生更多片段）
    // 修改：从开头删除旧记录，保持最新记录在末尾
    if (currentHistory.length > 30) {
      currentHistory = currentHistory.slice(-30);
    }

    // 先更新历史记录数据
    this.setData({
      historyResults: currentHistory
    });

    // 使用专门的滚动方法确保滚动到底部
    this.scrollHistoryToBottom();

    console.log('添加到历史记录:', historyItem.text, '时间:', historyItem.time, '来自服务器:', historyItem.fromServer, '记录总数:', currentHistory.length);
  },

  // 清除识别历史记录
  clearHistoryResults: function () {
    this.setData({
      historyResults: [],
      historyScrollTop: 0 // 重置滚动位置到顶部
    });
    console.log('已清除识别历史记录，滚动位置已重置');
  },

  onLoad: function () {
    try {
      // 判断设备类型并缓存平台信息，避免重复计算
      const systemInfo = wx.getSystemInfoSync();
      console.log('系统信息:', systemInfo);

      //   PC端检测逻辑
      let currentClient = "mobile"; // 默认移动端
      if (systemInfo.platform === 'devtools' ||
        systemInfo.platform === 'windows' ||
        (systemInfo.system && systemInfo.system.toLowerCase().includes('windows'))) {
        currentClient = "windows";
        console.log('  检测到PC端环境:', systemInfo.platform);
      }

      // 缓存平台信息，消除34处重复的平台判断开销
      this.platformInfo = {
        isIOS: systemInfo.platform === 'ios',
        platformName: systemInfo.platform === 'ios' ? 'iOS' : 'Android',
        // 音频配置已统一，无需平台差异化选择
        audioConfig: this.data.audioConfig
      };

      this.setData({
        isIOS: this.platformInfo.isIOS,
        currentClient: currentClient, // 添加PC端标识
        isProcessingRecording: false,
        networkRetryCount: 0,
        maxNetworkRetry: 3
      });
      console.log('当前设备类型:', systemInfo.platform, '已缓存平台信息');

      // 监听网络状态变化
      wx.onNetworkStatusChange((res) => {
        console.log('网络状态变化:', res);
        if (!res.isConnected) {
          wx.showToast({
            title: '网络连接断开',
            icon: 'none',
            duration: 2000
          });
        } else {
          console.log('网络已恢复，类型:', res.networkType);
          // 重置网络重试计数
          this.setData({
            networkRetryCount: 0
          });
        }
      });

      // 初始化录音管理器
      this.initRecorderManager();

      // 优化：页面加载时清除历史记录，确保每次进入都是干净状态
      this.clearHistoryResults();
      console.log('页面加载完成，历史记录已清除');

      // 测试数据已移除，现在使用真实的识别结果
    } catch (error) {
      console.error('onLoad初始化失败:', error);
      wx.showToast({
        title: '初始化失败，请重试',
        icon: 'none'
      });
    }
  },

  onHide: function () {
    console.log('页面隐藏，根据微信小程序平台限制进行适配处理');

    // 记录页面隐藏时间和当前状态，用于后续恢复
    const currentState = {
      pageHideTime: Date.now(),
      isPageVisible: false,
      wasRecording: this.data.isluyin,
      wasConnected: !!this.data.socketTaskId,
      currentText: this.data.textcontent,
      recordingStartTime: this.data.recordingStartTime || 0
    };

    this.setData(currentState);

    // 保存当前识别状态到本地存储，防止系统回收导致数据丢失
    if (this.data.isluyin) {
      try {
        wx.setStorageSync('voiceRecognitionState', {
          wasRecording: true,
          hideTime: currentState.pageHideTime,
          currentText: this.data.textcontent,
          historyResults: this.data.historyResults,
          recordingStartTime: this.data.recordingStartTime,
          keepScreenOnEnabled: this.data.keepScreenOnEnabled // 保存屏幕常亮状态
        });
        console.log('录音状态已保存到本地存储');
      } catch (error) {
        console.error('保存录音状态失败:', error);
      }
    }

    // 🔆 页面隐藏时保持屏幕常亮状态，因为录音可能在后台继续
    // 注意：微信小程序后台5秒后会挂起，但屏幕常亮设置会保持
    if (this.data.isluyin && this.data.keepScreenOnEnabled) {
      console.log('🔆 录音中页面隐藏，保持屏幕常亮状态');
      // 不关闭屏幕常亮，让它在后台保持
    }

    // 微信小程序平台限制：录音API在后台会被强制停止
    // 因此我们需要优雅地处理这个限制，而不是试图绕过它
    if (this.data.isluyin || this.data.recorderState === 'recording') {
      console.log('检测到录音状态，由于平台限制将被系统停止');

      // 发送最后的音频数据（如果有）
      if (this.data.socketTaskId && this.data.audioBuffer.length > 0) {
        try {
          // 发送缓冲区中的音频数据
          this.data.audioBuffer.forEach(buffer => {
            this.data.socketTaskId.send({
              data: buffer,
              success: () => console.log('发送缓冲音频数据成功'),
              fail: (error) => console.error('发送缓冲音频数据失败:', error)
            });
          });
        } catch (error) {
          console.error('发送最后音频数据失败:', error);
        }
      }

      // 不主动停止录音，让系统自然停止，但标记状态
      this.setData({
        backgroundInterrupted: true // 标记为后台中断
      });
    }

    // WebSocket连接：保持连接但准备重连机制
    // 微信小程序后台5秒后会挂起，但WebSocket可能保持更长时间
    if (this.data.socketTaskId) {
      console.log('保持WebSocket连接，准备后台挂起适配');
      // 不主动关闭，让系统决定何时回收
    }

    console.log('页面隐藏处理完成，已适配微信小程序平台限制');
  },

  onUnload: function () {
    console.log('页面卸载，清理所有资源和历史记录');

    // 🔆 安全恢复屏幕常亮设置，确保不会影响用户的其他应用
    this.safeDisableKeepScreenOn();

    // 页面卸载时清除历史记录
    this.clearHistoryResults();

    this.closeWebSocket(false);

    // 清理智能录音超时定时器
    this.clearIntelligentStopTimer();

    if (this.data.wsTimeoutTimer) {
      clearTimeout(this.data.wsTimeoutTimer);
    }

    if (this.data.connectionTimeoutId) {
      clearTimeout(this.data.connectionTimeoutId);
      this.setData({
        connectionTimeoutId: null
      });
    }

    if (this.data.frameCheckTimer) {
      clearInterval(this.data.frameCheckTimer);
    }

    if (this.data.keepAliveTimer) {
      clearInterval(this.data.keepAliveTimer);
    }

    if (this.data.recorderHealthCheckTimer) {
      clearInterval(this.data.recorderHealthCheckTimer);
    }

    if (this.data.iosWebSocketTimer) {
      clearInterval(this.data.iosWebSocketTimer);
    }

    if (this.data.recordingStopTimeout) {
      clearTimeout(this.data.recordingStopTimeout);
    }

    if (this.recorderManager && this.data.recorderState === 'recording') {
      try {
        this.recorderManager.stop();
      } catch (error) {
        console.error('停止录音失败:', error);
      }
    }

    // 优化：使用公共函数减少重复代码
    this.resetToInitialState({
      recorderState: 'idle',
      wsConnectionClosed: true,
      historyResults: []
    });

    console.log('页面卸载，已清理所有资源');
  },

  connectWebSocket: function () {
    const that = this;

    //  PC端不使用WebSocket，直接返回
    if (that.data.currentClient == "windows") {
      console.log('PC端检测到，不使用WebSocket连接');
      return new Promise((resolve) => {
        // PC端不需要WebSocket连接，直接返回失败让录音流程继续
        resolve(false);
      });
    }

    // 移动端继续使用WebSocket连接
    // 使用更高精度的随机数，减少冲突可能性
    var sid = parseInt(Math.random() * 10000 + Date.now() % 1000).toString();
    return new Promise((resolve, reject) => {
      // 如果已经存在连接，先关闭它
      if (that.data.socketTaskId) {
        try {
          that.data.socketTaskId.close({
            complete: function () {
              that.setData({
                socketTaskId: null,
                isConnected: false
              });
              console.log('已关闭现有WebSocket连接');
            }
          });
        } catch (e) {
          console.error('关闭现有WebSocket连接失败:', e);
          that.setData({
            socketTaskId: null,
            isConnected: false
          });
        }
      }

      // 优化：批量重置连接状态，减少setData调用
      that.updateState({
        isConnecting: true,
        wsConnectionClosed: false, // 添加新状态标记，表示连接未被主动关闭
        // 重置音频缓冲区和计数器，确保新连接从干净状态开始
        audioBuffer: [],
        lastSendTime: 0,
        audioDataSent: false
      });

      try {
        console.log('🔌 尝试创建WebSocket连接', {
          timestamp: new Date().toISOString(),
          sid: sid,
          isIOS: that.data.isIOS,
          isluyin: that.data.isluyin,
          recorderState: that.data.recorderState,
          wsConnectionClosed: that.data.wsConnectionClosed
        });

        // 统一使用iOS端的认证凭证，简化连接参数
        const userid = "test123";
        const token = "test1";

        // 使用闽南语专用WebSocket接口，仅保留必需参数
        const wsUrl = "wss://mny.talentedsoft.com/dotcwsasr?userid=" + userid + "&token=" + token + "&sid=" + sid;

        console.log('🔗 统一认证参数WebSocket连接', {
          userid: userid,
          token: token,
          sid: sid,
          url: wsUrl,
          platform: that.platformInfo.platformName // 使用缓存的平台名称
        });

        // 优化连接超时处理，进一步减少超时时间
        let connectionTimeoutId = setTimeout(function () {
          console.log('   WebSocket连接超时');
          if (that.data.isConnecting && !that.data.isConnected) {
            that.setData({
              isConnecting: false,
              connectionTimeoutId: null
            });
            reject(new Error('WebSocket连接超时'));
          }
        }, 3000); // 进一步减少到3秒超时，提高响应速度

        // 保存超时ID到data中，确保可以在其他地方访问
        that.setData({
          connectionTimeoutId: connectionTimeoutId
        });

        let socketTask = wx.connectSocket({
          url: wsUrl,
          success: function () {
            console.log('WebSocket连接创建成功', {
              timestamp: new Date().toISOString()
            });
          },
          fail: function (error) {
            console.error('WebSocket连接创建失败：', error);
            // 清除连接超时定时器
            clearTimeout(that.data.connectionTimeoutId);
            that.setData({
              isConnecting: false,
              connectionTimeoutId: null
            });
            reject(error);
          }
        });

        if (!socketTask) {
          console.error('创建WebSocket连接失败，socketTask为空');
          // 清除连接超时定时器
          clearTimeout(that.data.connectionTimeoutId);
          that.setData({
            isConnecting: false,
            connectionTimeoutId: null
          });
          reject(new Error('创建WebSocket连接失败'));
          return;
        }

        // 保存socketTask对象并初始化相关状态
        that.setData({
          socketTaskId: socketTask,
          lastMessageTime: 0, // 添加最后收到消息的时间
          messageCount: 0, // 添加消息计数
          // 重置文本缓存以便更好地合并文本
          currentText: "",
          fullText: ""
        });

        // 注册WebSocket事件处理函数
        socketTask.onOpen(function () {
          console.log('WebSocket连接已打开', {
            timestamp: new Date().toISOString(),
            isIOS: that.data.isIOS
          });

          // 清除连接超时定时器
          clearTimeout(that.data.connectionTimeoutId);
          that.setData({
            connectionTimeoutId: null
          });

          // 再次检查socketTask是否仍然存在，使用本地变量而非data中的值
          if (!socketTask) {
            console.error('WebSocket连接已打开，但socketTask已不存在');
            reject(new Error('WebSocket状态不一致'));
            return;
          }

          // 检查连接是否已被关闭
          if (that.data.wsConnectionClosed) {
            console.log('WebSocket连接已打开，但已被标记为关闭状态，忽略此连接');
            try {
              socketTask.close();
            } catch (e) {
              console.error('关闭多余WebSocket连接失败:', e);
            }
            reject(new Error('WebSocket已被关闭'));
            return;
          }

          // 优化：使用批量连接状态更新
          that.updateConnectionState({
            isConnected: true,
            isConnecting: false
          });

          that.updateState({
            'streamStatus.isStreaming': true
          });

          // 保活机制已删除 - 避免干扰录音识别

          // 移除启动WebSocket超时检测
          resolve();
        });

        socketTask.onMessage(function (res) {
          // 检查连接是否已被关闭
          if (that.data.wsConnectionClosed) {
            console.log('WebSocket连接已被标记为关闭状态，忽略此消息');
            return;
          }

          try {
            // 更新消息计数和时间戳，使用高精度时间戳
            const receiveTime = Date.now();
            that.setData({
              lastMessageTime: receiveTime,
              messageCount: that.data.messageCount + 1
            });

            // 为了调试iOS frameBuffer识别，暂时输出所有消息
            console.log('🔔 收到WebSocket消息', {
              timestamp: new Date().toISOString(),
              messageLength: res.data ? res.data.length : 0,
              messageCount: that.data.messageCount,
              isIOS: that.data.isIOS,
              rawData: res.data ? res.data.substring(0, 200) : 'null' // 显示前200个字符
            });

            // 处理特殊字符
            let newStr;
            if (typeof res.data === 'string') {
              // 优化字符串处理，减少不必要的替换操作
              newStr = res.data.includes('\n') ? res.data.replace(/\n/g, "~") : res.data;
            } else {
              console.log('收到非文本消息，可能是二进制数据');
              return;
            }

            let ret_json;
            try {
              // 使用更高效的JSON解析
              ret_json = JSON.parse(newStr);

              // 只在调试模式或每10条消息时输出详细日志
              if (that.data.messageCount % 10 === 0) {
                console.log('解析后的WebSocket消息:', ret_json);
              }

              // 文本预处理，优化替换逻辑
              if (ret_json.result) {
                let content = ret_json.result;
                // 只在需要时进行替换，减少不必要的字符串操作
                if (content.includes('~')) {
                  content = content.replace(/~/g, "\n");
                }
                if (content.includes('|')) {
                  content = content.replace(/\|/g, " ");
                }
                ret_json.result = content;
              }
            } catch (parseError) {
              console.error('解析WebSocket消息失败:', parseError, '原始数据长度:', newStr.length);

              // 尝试直接处理文本内容（安卓设备可能不是标准JSON格式）
              if (newStr && !that.data.isIOS) {
                // 使用更高效的正则表达式提取结果
                const resultMatch = newStr.match(/result\s*:\s*["']([^"']*)["']/i);
                if (resultMatch && resultMatch[1]) {
                  const textResult = resultMatch[1]
                    .replace(/~/g, "\n")
                    .replace(/\|/g, " ");
                  // 创建一个简单的结果对象
                  ret_json = {
                    result: textResult,
                    errCode: "0" // 假设为最终结果
                  };
                } else {
                  return;
                }
              } else {
                return;
              }
            }

            // iOS和Android统一使用相同的处理函数，确保结果处理一致
            console.log('📱 统一处理WebSocket结果', {
              isIOS: that.data.isIOS,
              hasResult: !!ret_json.result,
              errCode: ret_json.errCode
            });

            // iOS和Android都使用Android的处理逻辑
            that.processAndroidWebSocketResult(ret_json, receiveTime);
          } catch (error) {
            console.error('处理WebSocket消息失败：', error, '原始数据:', res.data);
          }
        });

        socketTask.onError(function (error) {
          console.error('WebSocket错误：', error, {
            isIOS: that.data.isIOS,
            timestamp: new Date().toISOString()
          });

          // 清除连接超时定时器
          if (that.data.connectionTimeoutId) {
            clearTimeout(that.data.connectionTimeoutId);
            that.setData({
              connectionTimeoutId: null
            });
          }

          that.setData({
            isConnecting: false
          });

          // 处理WebSocket错误
          that.handleWebSocketError(error);

          reject(error);
        });

        socketTask.onClose(function (res) {
          console.log('WebSocket连接已关闭', {
            timestamp: new Date().toISOString(),
            isIOS: that.data.isIOS,
            messageCount: that.data.messageCount,
            lastMessageTime: that.data.lastMessageTime ? new Date(that.data.lastMessageTime).toISOString() : '无',
            code: res.code,
            reason: res.reason
          });

          // 清除连接超时定时器
          if (that.data.connectionTimeoutId) {
            clearTimeout(that.data.connectionTimeoutId);
            that.setData({
              connectionTimeoutId: null
            });
          }

          that.setData({
            isConnected: false,
            isConnecting: false,
            socketTaskId: null
          });

          // 已删除自动重连逻辑，连接失败时直接使用HTTP备用方案
        });
      } catch (error) {
        console.error('创建WebSocket连接时出错：', error);
        // 清除连接超时定时器
        if (that.data.connectionTimeoutId) {
          clearTimeout(that.data.connectionTimeoutId);
          that.setData({
            connectionTimeoutId: null
          });
        }
        that.setData({
          isConnecting: false
        });
        reject(error);
      }
    });
  },



  handleWebSocketError: function (error) {
    const that = this;
    console.error('处理WebSocket错误：', {
      error: error,
      socketTaskId: that.data.socketTaskId ? '存在' : '不存在',
      timestamp: new Date().toISOString(),
      status: that.data.streamStatus,
      errorType: error.errMsg ? 'wx.error' : 'js.error',
      isIOS: that.data.isIOS
    });

    // 关闭现有连接（如果存在），错误情况下不发送end标签
    if (that.data.socketTaskId) {
      try {
        that.data.socketTaskId.close({
          complete: function () {
            that.setData({
              socketTaskId: null,
              isConnected: false
            });
            console.log('因错误关闭WebSocket连接');
          }
        });
      } catch (closeError) {
        console.error('关闭WebSocket连接时出错：', closeError);
        that.setData({
          socketTaskId: null,
          isConnected: false
        });
      }
    } else {
      // 如果socketTaskId不存在，也需要确保状态正确
      that.setData({
        socketTaskId: null,
        isConnected: false
      });
    }

    // 简化错误处理：直接标记连接失败，使用HTTP备用方案
    that.setData({
      socketTaskId: null,
      isConnected: false,
      isConnecting: false,
      wsConnectionFailed: true,
      streamStatus: {
        isStreaming: false,
        isRecording: false,
        isConnected: false
      }
    });

    // 显示提示
    wx.showToast({
      title: '连接失败，将使用备用方案',
      icon: 'none',
      duration: 2000
    });
  },

  // 简化的WebSocket关闭函数
  closeWebSocket: function (sendEndMarker = false) {
    const that = this;

    console.log('🔌 关闭WebSocket连接', {
      sendEndMarker: sendEndMarker,
      hasConnection: !!that.data.socketTaskId,
      timestamp: new Date().toISOString()
    });

    // 标记连接为已关闭状态
    that.setData({
      wsConnectionClosed: true
    });

    if (!that.data.socketTaskId) {
      console.log('WebSocket连接不存在，无需关闭');
      return;
    }

    // 保存当前的识别文本
    const finalText = that.data.fullText || that.data.textcontent;

    // 重置连接状态的通用函数
    const resetConnectionState = function () {
      that.setData({
        socketTaskId: null,
        isConnected: false,
        isConnecting: false,
        textcontent: finalText,
        streamStatus: {
          isStreaming: false,
          isRecording: false,
          isConnected: false
        }
      });
    };

    try {
      //  PC端不使用WebSocket，直接重置状态
      if (that.data.currentClient == "windows") {
        console.log(' PC端不使用WebSocket，直接重置状态');
        resetConnectionState();
        return;
      }

      // 移动端WebSocket处理
      if (sendEndMarker && that.data.socketTaskId && typeof that.data.socketTaskId.send === 'function') {
        // 发送end标记后关闭连接
        console.log(' 发送end标记');
        that.data.socketTaskId.send({
          data: 'end',
          success: function () {
            console.log(' end标记发送成功');
            // 延迟关闭连接，确保end标记被处理
            setTimeout(function () {
              that.data.socketTaskId.close({
                complete: resetConnectionState
              });
            }, DELAYS.WEBSOCKET_CLOSE);
          },
          fail: function (error) {
            console.error('   end标记发送失败:', error);
            // 发送失败也关闭连接
            that.data.socketTaskId.close({
              complete: resetConnectionState
            });
          }
        });
      } else {
        // 直接关闭连接，不发送end标记
        console.log('🔒 直接关闭连接');
        if (that.data.socketTaskId && typeof that.data.socketTaskId.close === 'function') {
          that.data.socketTaskId.close({
            complete: resetConnectionState
          });
        } else {
          resetConnectionState();
        }
      }
    } catch (error) {
      console.error('   关闭WebSocket时出错:', error);
      resetConnectionState();
    }
  },



  // 修改录音管理器初始化方法，增强错误处理
  initRecorderManager: function () {
    const that = this;

    // 如果已经存在录音管理器，先尝试释放
    if (that.recorderManager) {
      try {
        // 尝试停止可能正在进行的录音
        if (that.data.recorderState === 'recording') {
          that.recorderManager.stop();
        }

        // 解绑事件监听，避免重复监听
        if (that.recorderManager.offStart) that.recorderManager.offStart();
        if (that.recorderManager.offStop) that.recorderManager.offStop();
        if (that.recorderManager.offError) that.recorderManager.offError();
        if (that.recorderManager.offFrameRecorded) that.recorderManager.offFrameRecorded();

        console.log('释放现有录音管理器');
      } catch (error) {
        console.error('释放录音管理器出错:', error);
      }
    }

    // 创建新的录音管理器
    that.recorderManager = wx.getRecorderManager();

    // 添加iOS设备的帧数据检测和健康状态检查
    // 优化：使用内存变量减少setData调用
    that._lastFrameTime = 0;
    that._frameReceived = false;

    that.setData({
      frameCheckTimer: null,
      recorderState: 'idle', // 初始化录音状态
      recorderHealthCheckTimer: null // 添加录音健康检查定时器
    });

    // 添加offStop方法，如果不存在则创建一个空方法
    if (!that.recorderManager.offStop) {
      that.recorderManager.offStop = function () {
        console.log('自定义offStop方法被调用');
        // 这是一个空方法，仅用于兼容性
      };
    }

    // 录音健康检查定时器，iOS设备专用
    if (that.data.isIOS) {
      const healthCheckTimer = setInterval(function () {
        // 只在录音状态下检查
        if (that.data.recorderState === 'recording') {
          const now = Date.now();
          const lastFrameTime = that._lastFrameTime || 0;

          // 如果超过3秒没有接收到帧数据，认为录音可能已经卡住
          if (now - lastFrameTime > DELAYS.HEALTH_CHECK && that.data.isluyin) {
            console.warn('iOS录音健康检查：超过3秒未收到帧数据，尝试重置录音');

            try {
              // 停止当前录音
              that.recorderManager.stop();

              // 短暂延迟后重新开始录音
              setTimeout(function () {
                if (that.data.isluyin) {
                  // 重置录音状态
                  that.setData({
                    recorderState: 'idle'
                  });

                  // 重新开始录音，使用iOS优化配置
                  const options = {
                    sampleRate: that.data.iosAudioConfig.sampleRate,
                    numberOfChannels: that.data.iosAudioConfig.numberOfChannels,
                    encodeBitRate: that.data.iosAudioConfig.encodeBitRate,
                    format: that.data.iosAudioConfig.format,
                    frameSize: that.data.iosAudioConfig.frameSize,
                    duration: that.data.iosAudioConfig.duration
                  };

                  that.recorderManager.start(options);
                  console.log('iOS录音健康检查：已重新启动录音');
                }
              }, DELAYS.RECORDING_RESTART);
            } catch (error) {
              console.error('iOS录音健康检查：重置录音失败', error);
            }
          }
        }
      }, DELAYS.HEALTH_CHECK_INTERVAL); // 每秒检查一次

      that.setData({
        recorderHealthCheckTimer: healthCheckTimer
      });
    }

    // 帧数据回调，iOS和Android统一处理流式识别
    that.recorderManager.onFrameRecorded(function (res) {
      // 立即输出日志，确认回调被触发
      console.log('🎤 onFrameRecorded回调被触发!', {
        isIOS: that.data.isIOS,
        timestamp: new Date().toISOString(),
        hasFrameBuffer: !!res.frameBuffer,
        frameBufferSize: res.frameBuffer ? res.frameBuffer.byteLength : 0
      });

      // 优化：减少高频setData调用，只在内存中更新时间戳
      // 避免每次帧数据都触发页面渲染
      that._lastFrameTime = Date.now();
      that._frameReceived = true;

      //  PC端使用HTTP模式，跳过实时帧数据发送
      if (that.data.currentClient == "windows") {
        console.log(' PC端使用HTTP模式，跳过实时帧数据发送');
        return;
      }

      // 检查是否有有效的WebSocket连接
      if (!that.data.socketTaskId) {
        console.log('WebSocket未连接，跳过帧数据处理');
        return;
      }

      const {
        frameBuffer,
        isLastFrame
      } = res;

      // 输出音频帧信息，iOS和Android统一处理
      console.log('收到音频帧数据:', {
        frameSize: frameBuffer ? frameBuffer.byteLength : 0,
        isLastFrame: isLastFrame,
        isIOS: that.data.isIOS,
        timestamp: new Date().toISOString()
      });

      if (!frameBuffer || !frameBuffer.byteLength) {
        console.warn('音频帧数据为空或长度为0');
        return;
      }

      // 更新音频数据发送状态
      const now = Date.now();
      that.setData({
        audioDataSent: true,
        lastAudioDataTime: now
      });

      // iOS和Android统一的流式识别处理策略
      const timeSinceLastSend = now - (that.data.lastSendTime || 0);

      // 使用缓存的平台信息优化发送策略
      const minSendInterval = that.platformInfo.isIOS ? 20 : 40; // iOS更频繁发送
      const maxBufferSize = that.platformInfo.isIOS ? 2 : 3; // iOS缓冲区更小

      // 如果距离上次发送时间太短且不是最后一帧，先缓存数据
      if (!isLastFrame && timeSinceLastSend < minSendInterval && that.data.audioBuffer.length < maxBufferSize) {
        that.data.audioBuffer.push(frameBuffer);
        console.log(`${that.platformInfo.platformName}缓存音频数据，当前缓冲区大小:`, that.data.audioBuffer.length);
        return;
      }

      // 更新最后发送时间
      that.setData({
        lastSendTime: now
      });

      // 准备要发送的数据
      let dataToSend = frameBuffer;

      // 如果缓冲区有数据，优先发送缓冲区数据
      if (that.data.audioBuffer.length > 0) {
        // 使用缓存的平台信息优化数据发送策略
        dataToSend = that.platformInfo.isIOS ?
          that.data.audioBuffer.shift() : // iOS使用FIFO
          that.data.audioBuffer[that.data.audioBuffer.length - 1]; // Android使用最新数据

        // 将当前帧添加到缓冲区（如果不是从缓冲区取出的数据）
        if (!that.platformInfo.isIOS) {
          that.data.audioBuffer.push(frameBuffer);
        }

        // Android清空缓冲区，iOS保持队列
        if (!that.platformInfo.isIOS) {
          that.data.audioBuffer = [];
        }
      }

      // 使用WebSocket发送音频数据进行实时识别
      that.data.socketTaskId.send({
        data: dataToSend,
        success: function () {
          console.log(`${that.platformInfo.platformName}发送音频数据成功，数据长度:`, dataToSend.byteLength, '字节');
        },
        fail: function (error) {
          console.error(`${that.platformInfo.platformName}发送音频数据失败：`, error);
          // 失败时将数据加入重传队列
          if (dataToSend) {
            that.data.audioBuffer.push(dataToSend);
          }
          // 已删除重连逻辑，音频发送失败时直接使用HTTP备用方案
        }
      });
    });

    that.recorderManager.onStart(function () {
      console.log('录音开始', {
        timestamp: new Date().toISOString(),
        platform: that.platformInfo.platformName,
        config: that.platformInfo.audioConfig
      });

      // 更新全局录音状态
      // 优化：使用内存变量减少setData调用
      that._lastFrameTime = Date.now();
      that.setData({
        recorderState: 'recording'
      });
    });

    that.recorderManager.onStop(function (res) {
      console.log('录音停止', {
        timestamp: new Date().toISOString(),
        duration: res.duration,
        fileSize: res.fileSize,
        tempFilePath: res.tempFilePath ? '有临时文件' : '无临时文件',
        recorderState: that.data.recorderState,
        isIOS: that.data.isIOS,
        isRecording: that.data.isluyin
      });

      // 更新全局录音状态
      that.setData({
        recorderState: 'idle'
      });

      //   根据不同平台处理录音结果
      if (that.data.currentClient == "windows") {
        // PC端使用专业闽南语识别处理录音文件
        console.log('  PC端录音停止，使用专业闽南语识别处理');
        if (res.tempFilePath) {
          that.handlePcHttpRecognition(res.tempFilePath);
        } else {
          console.error('  PC端录音文件路径为空');
          that.handleRecognitionError();
        }
      } else if (that.data.isIOS) {
        // iOS设备现在使用基于frameBuffer的流式识别，不需要特殊处理录音停止
        console.log('📱 iOS设备录音停止，使用frameBuffer流式识别，无需特殊处理');
        // 注意：不再调用handleWebSocketRecorderStop，避免旧的分段识别逻辑
      } else {
        // 移动端：只使用WebSocket进行实时流式识别，不使用HTTP备用
        console.log('📱 移动端录音停止，使用WebSocket流式识别');
      }

      // 不要在这里修改文本内容，保持最后的识别结果
      that.setData({
        isluyin: false,
        isStreaming: false,
        streamStatus: {
          isStreaming: false,
          isRecording: false,
          isConnected: false
        }
      });
    });

    that.recorderManager.onError(function (error) {
      console.error('录音错误：', error, {
        recorderState: that.data.recorderState,
        isRecording: that.data.isluyin,
        isProcessingRecording: that.data.isProcessingRecording
      });

      // 更新全局录音状态
      that.setData({
        recorderState: 'idle',
        isProcessingRecording: false // 确保处理标记被清除
      });

      // 对于iOS设备，尝试重置录音管理器
      if (that.data.isIOS && that.data.isluyin) {
        console.log('iOS设备录音错误，尝试恢复');

        // 短暂延迟后尝试重新开始录音
        setTimeout(function () {
          if (that.data.isluyin) {
            try {
              // 如果WebSocket连接还存在，尝试重新开始录音
              if (that.data.socketTaskId) {
                const options = {
                  sampleRate: that.data.iosAudioConfig.sampleRate,
                  numberOfChannels: that.data.iosAudioConfig.numberOfChannels,
                  encodeBitRate: that.data.iosAudioConfig.encodeBitRate,
                  format: that.data.iosAudioConfig.format,
                  frameSize: that.data.iosAudioConfig.frameSize,
                  duration: that.data.iosAudioConfig.duration
                };

                that.recorderManager.start(options);
                console.log('iOS设备录音错误恢复：已重新启动录音');
              }
            } catch (startError) {
              console.error('尝试恢复录音失败:', startError);
            }
          }
        }, DELAYS.RECORDING_RESTART);
      } else {
        // 对于安卓设备或非录音状态，显示错误提示
        if (error.errMsg && (error.errMsg.indexOf('recorder not start') !== -1 || error.errMsg.indexOf('recorder already start') !== -1)) {
          console.log('录音状态错误，忽略');
          return;
        }

        wx.showToast({
          title: '录音出错，请重试',
          icon: 'none'
        });

        that.closeWebSocket(false); // 错误时不发送end标记
        that.setData({
          isluyin: false,
          isanzhu: true,
          textcontent: MESSAGES.INITIAL
        });
      }
    });
  },

  //   PC端HTTP识别处理（与mny项目完全一致）
  handlePcHttpRecognition: function (tempFilePath) {
    const that = this;

    // 定义URL和formData
    let url = "https://tc.talentedsoft.com:58120/ajax/wechat/dotcasr_proxy";
    const formData = {
      "userid": "tc_wx_yun8888",
      "token": "wx5678"
    };

    // PC端添加闽南语识别参数
    if (that.data.currentClient == "windows") {
      formData["asr_type"] = "minnanyu";
      console.log('  PC端使用经过验证的URL:', url);
      console.log('  PC端参数:', formData);
    }

    // 显示识别中状态和加载动画
    that.setData({
      'isshibie': true,
      'textcontent': '正在进行闽南语识别...',
      'isluyin': false,
      'isanzhu': false
    });

    // 🖥️ PC端显示加载动画
    wx.showLoading({
      title: '正在识别闽南语...',
      mask: true // 防止用户操作
    });

    // 文件大小检查
    wx.getFileInfo({
      filePath: tempFilePath,
      success: function (fileInfo) {
        console.log('  文件信息:', Math.round(fileInfo.size / 1024) + 'KB');
        if (fileInfo.size < 1000) {
          console.warn('⚠️ 文件过小，可能是静音:', fileInfo.size + 'B');
        }
      },
      fail: function (err) {
        console.error('  文件信息获取失败:', err.errMsg);
      }
    });

    wx.uploadFile({
      url: url,
      filePath: tempFilePath,
      header: {
        "Content-Type": "multipart/form-data"
      },
      name: 'file',
      formData: formData,
      // 移除timeout参数，与mny项目保持一致
      success: function (res) {
        that.handlePcHttpResponse(res);
      },
      fail: function (err) {
        console.error('  PC端识别失败:', err.errMsg);
        that.handlePcHttpError(err);
      }
    });
  },

  //   PC端HTTP响应处理
  handlePcHttpResponse: function (res) {
    const that = this;

    console.log('  PC端闽南语识别响应:', res.statusCode);
    console.log('  PC端闽南语识别响应内容:', res.data);
    console.log('  PC端闽南语识别响应类型:', typeof res.data);

    // 检查HTTP状态码
    if (res.statusCode !== 200) {
      console.error('  PC端HTTP请求失败，状态码:', res.statusCode);
      that.setData({
        'isanzhu': false,
        'isshibie': false,
        'textcontent': `闽南语识别失败(${res.statusCode})`
      });
      return;
    }

    if (res.data) {
      try {
        const data = JSON.parse(res.data);
        console.log('  识别响应解析:', {
          state: data.state,
          hasMsg: !!data.msg
        });

        // 适配服务端返回格式：{"msg":"识别结果","state":"Success"}
        if (data.state === "Success") {
          if (!data.msg || data.msg.trim() === '') {
            // 🖥️ 识别结果为空，隐藏加载动画
            wx.hideLoading();
            console.log('  识别成功但结果为空');
            that.resetToErrorState();

          } else {
            // 🖥️ 识别成功，隐藏加载动画
            wx.hideLoading();

            let date = new Date();
            const formattedTime = that.dateFormat("YYYY-mm-dd HH:MM", date);
            console.log('  PC端闽南语识别结果 [' + formattedTime + ']: ' + data.msg);

            that.addToHistory(data.msg, formattedTime);
            that.setData({
              'isanzhu': false,
              'isshibie': false,
              'textcontent': data.msg
            });


          }
        } else {
          // 🖥️ 识别服务错误，隐藏加载动画
          wx.hideLoading();
          console.error('  识别服务错误:', {
            state: data.state,
            response: data
          });
          that.resetToErrorState();

        }
      } catch (error) {
        // 🖥️ JSON解析失败，隐藏加载动画
        wx.hideLoading();
        console.error('  JSON解析失败:', {
          error: error.message,
          dataLength: res.data?.length
        });
        that.handleRecognitionError();
      }
    } else {
      // 🖥️ 响应数据为空，隐藏加载动画
      wx.hideLoading();
      console.error('  响应数据为空:', {
        statusCode: res.statusCode
      });
      that.handleRecognitionError();
    }
  },

  //   PC端HTTP错误处理
  handlePcHttpError: function (err) {
    const that = this;

    // 🖥️ 网络请求失败，隐藏加载动画
    wx.hideLoading();

    console.error('  PC端闽南语识别请求失败:', err.errMsg);

    let errorMessage = '闽南语识别失败';
    if (err.errMsg && err.errMsg.includes('timeout')) {
      errorMessage = '闽南语识别超时';
    } else if (err.errMsg && err.errMsg.includes('network')) {
      errorMessage = '网络连接失败';
    }

    that.setData({
      'isanzhu': false,
      'isshibie': false,
      'textcontent': errorMessage
    });
  },



  // 修改错误处理方法
  handleRecognitionError: function () {
    const that = this;
    wx.hideLoading();
    wx.showToast({
      title: "识别服务请求异常，请重试",
      icon: "none",
      duration: 2000
    });
    // 优化：使用公共函数减少重复代码
    that.resetToInitialState({
      isanzhu: false // 设置为false不显示tipimg.png
    });
  },

  // 新增：检查并恢复录音状态的函数
  checkAndRestoreRecordingState: function () {
    const that = this;

    console.log('检查录音状态恢复', {
      isluyin: that.data.isluyin,
      recorderState: that.data.recorderState,
      hasSocketConnection: !!that.data.socketTaskId,
      pageHideTime: that.data.pageHideTime,
      currentTime: Date.now()
    });

    // 如果之前在录音状态，需要检查是否需要恢复
    if (that.data.isluyin || that.data.recorderState === 'recording') {
      const hideTime = that.data.pageHideTime || 0;
      const currentTime = Date.now();
      const hideDuration = currentTime - hideTime;

      console.log('检测到之前在录音状态', {
        hideDuration: hideDuration,
        hideDurationSeconds: Math.round(hideDuration / 1000)
      });

      // 如果隐藏时间超过5分钟，可能WebSocket连接已断开，需要重新连接
      if (hideDuration > 5 * 60 * 1000) {
        console.log('页面隐藏时间超过5分钟，重新建立WebSocket连接');
        that.restoreRecordingWithReconnect();
      } else {
        // 短时间隐藏，检查WebSocket连接状态
        if (!that.data.socketTaskId || !that.data.isConnected) {
          console.log('WebSocket连接已断开，重新连接');
          that.restoreRecordingWithReconnect();
        } else {
          console.log('WebSocket连接正常，录音状态已保持');
          // 确保录音管理器状态正确
          that.ensureRecorderManagerState();
        }
      }
    } else {
      console.log('之前未在录音状态，无需恢复');
    }
  },

  // 新增：通过重新连接恢复录音状态（适配平台限制版本）
  restoreRecordingWithReconnect: function (savedState) {
    const that = this;

    console.log('开始恢复录音状态，重新建立连接');

    // 显示恢复中的提示
    wx.showLoading({
      title: '正在恢复录音...',
      mask: true
    });

    // 先关闭可能存在的旧连接
    if (that.data.socketTaskId) {
      that.closeWebSocket(false);
    }

    // 重新建立WebSocket连接
    that.connectWebSocket().then(() => {
      console.log('WebSocket重连成功，开始恢复录音');

      // 🔆 恢复屏幕常亮状态（如果之前开启了）
      if (savedState && savedState.keepScreenOnEnabled) {
        that.enableKeepScreenOn();
        console.log('🔆 已恢复屏幕常亮状态');
      }

      // 恢复录音状态
      that.setData({
        isluyin: true,
        isanzhu: false,
        textcontent: '正在录音中，点击结束录音',
        backgroundInterrupted: false,
        recordingStartTime: savedState ? savedState.recordingStartTime : Date.now()
      });

      // 重新启动录音
      try {
        const options = that.platformInfo.audioConfig;
        that.recorderManager.start(options);
        console.log('录音已恢复');

        // 重新启动智能超时定时器
        that.startIntelligentStopTimer();

        wx.hideLoading();
        wx.showToast({
          title: '录音已恢复',
          icon: 'success',
          duration: 1500
        });

        // 清除保存的状态
        try {
          wx.removeStorageSync('voiceRecognitionState');
        } catch (error) {
          console.error('清除保存状态失败:', error);
        }
      } catch (error) {
        console.error('恢复录音失败:', error);
        wx.hideLoading();
        wx.showToast({
          title: '录音恢复失败，请重新开始',
          icon: 'none',
          duration: 2000
        });
        that.resetToInitialState();
      }
    }).catch(error => {
      console.error('WebSocket重连失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '连接失败，请重新开始录音',
        icon: 'none',
        duration: 2000
      });
      that.resetToInitialState();
    });
  },

  // 新增：确保录音管理器状态正确
  ensureRecorderManagerState: function () {
    const that = this;

    // 检查录音管理器是否存在
    if (!that.recorderManager) {
      console.log('录音管理器不存在，重新初始化');
      that.initRecorderManager();
      return;
    }

    // 检查录音状态一致性
    if (that.data.isluyin && that.data.recorderState !== 'recording') {
      console.log('录音状态不一致，尝试修复');
      try {
        const options = that.platformInfo.audioConfig;
        that.recorderManager.start(options);
        that.setData({
          recorderState: 'recording'
        });
        console.log('录音状态已修复');
      } catch (error) {
        console.error('修复录音状态失败:', error);
      }
    }
  },

  // 新增：屏幕常亮控制函数
  enableKeepScreenOn: function () {
    const that = this;

    if (that.data.keepScreenOnEnabled) {
      console.log('屏幕常亮已经开启，无需重复设置');
      return;
    }

    wx.setKeepScreenOn({
      keepScreenOn: true,
      success: function () {
        that.setData({
          keepScreenOnEnabled: true
        });
        console.log('   屏幕常亮已开启，录音过程中屏幕将保持亮起');

        // 优化：简化用户提示，避免干扰录音体验
        // 屏幕常亮是后台功能，用户无需特别关注
        // wx.showToast({
        //   title: '已开启屏幕常亮',
        //   icon: 'success',
        //   duration: 1500
        // });
      },
      fail: function (error) {
        console.error('   开启屏幕常亮失败:', error);
        // 优化：屏幕常亮失败不影响录音功能，仅在控制台记录错误
        // 避免干扰用户的录音体验
        console.warn('屏幕常亮设置失败，但不影响录音功能正常使用');
      }
    });
  },

  disableKeepScreenOn: function () {
    const that = this;

    if (!that.data.keepScreenOnEnabled) {
      console.log('屏幕常亮未开启，无需关闭');
      return;
    }

    wx.setKeepScreenOn({
      keepScreenOn: false,
      success: function () {
        that.setData({
          keepScreenOnEnabled: false
        });
        console.log('   屏幕常亮已关闭，恢复正常屏幕设置');
      },
      fail: function (error) {
        console.error('   关闭屏幕常亮失败:', error);
        // 即使关闭失败，也要更新状态标记
        that.setData({
          keepScreenOnEnabled: false
        });
      }
    });
  },

  // 新增：安全的屏幕常亮恢复函数（用于异常情况）
  safeDisableKeepScreenOn: function () {
    const that = this;

    try {
      // 无论当前状态如何，都尝试关闭屏幕常亮
      wx.setKeepScreenOn({
        keepScreenOn: false,
        success: function () {
          console.log('🔧 安全模式：屏幕常亮已强制关闭');
        },
        fail: function (error) {
          console.error('🔧 安全模式：关闭屏幕常亮失败:', error);
        },
        complete: function () {
          // 无论成功失败，都重置状态标记
          that.setData({
            keepScreenOnEnabled: false
          });
        }
      });
    } catch (error) {
      console.error('🔧 安全模式：屏幕常亮控制异常:', error);
      // 确保状态标记被重置
      that.setData({
        keepScreenOnEnabled: false
      });
    }
  },

  onShow() {
    let that = this;
    try {
      wx.showShareMenu({
        withShareTicket: true
      });

      // 标记页面为可见状态
      that.setData({
        isPageVisible: true
      });

      // 检查是否需要恢复录音状态
      that.checkAndRestoreRecordingState();

      // 检查接口是否可用
      wx.getSetting({
        success(res) {
          console.log('录音权限状态:', res['scope.record']);
          if (!res['scope.record']) {
            // 接口调用询问
            wx.authorize({
              scope: 'scope.record',
              success() {
                console.log('录音权限授权成功');
                that.initRecorderManager();
              },
              fail(error) {
                console.error('录音权限授权失败:', error);
                wx.showModal({
                  title: '提示',
                  content: '需要录音权限才能使用语音识别功能',
                  showCancel: false,
                  success() {
                    wx.openSetting({
                      success: (res) => {
                        console.log('打开设置页面成功');
                        if (res.authSetting['scope.record']) {
                          that.initRecorderManager();
                        }
                      },
                      fail(error) {
                        console.error('打开设置页面失败:', error);
                      }
                    });
                  }
                });
              }
            });
          } else {
            console.log('已有录音权限，初始化录音管理器');
            that.initRecorderManager();
          }
        },
        fail(error) {
          console.error('获取录音权限状态失败:', error);
          wx.showToast({
            title: '获取权限失败，请重试',
            icon: 'none'
          });
        }
      });
    } catch (error) {
      console.error('onShow执行失败:', error);
      wx.showToast({
        title: '启动失败，请重试',
        icon: 'none'
      });
    }
  },



  // 将录音启动流程提取为单独的函数
  startRecordingProcess: function () {
    const that = this;

    // 🔆 开启屏幕常亮，防止录音过程中屏幕熄灭
    that.enableKeepScreenOn();

    // 清空之前的识别结果
    that.setData({
      isluyin: true,
      isanzhu: false,
      textcontent: MESSAGES.RECORDING,
      currentText: '', // 清空当前文本
      fullText: '', // 清空完整文本
      audioBuffer: [],
      wsConnectionFailed: false,
      audioDataSent: false, // 重置音频数据发送状态
      lastAudioDataTime: 0, // 重置最后音频数据时间
      recordingStartTime: Date.now(), // 记录录音开始时间
      streamStatus: {
        isStreaming: true, // 设置为true表示正在流式识别
        isRecording: true,
        isConnected: false
      }
    });

    // 重置帧接收状态（使用内存变量，减少setData调用）
    that._frameReceived = false;
    that._lastFrameTime = 0;

    // 已删除音频数据处理重置，统一使用onFrameRecorded流式识别

    // 根据平台选择连接方式
    if (that.data.currentClient == "windows") {
      // PC端直接开始录音，不使用WebSocket
      console.log('  PC端直接开始录音，不使用WebSocket');
      that.startRecordingDirectly();
    } else {
      // 移动端使用WebSocket连接
      that.connectWebSocket().then(() => {
        // 再次检查录音状态，防止在连接过程中录音被停止
        if (!that.data.isluyin) {
          console.log('WebSocket连接成功，但录音已停止，不开始录音');
          that.closeWebSocket(false); // 连接成功但录音已停止，不发送end标记
          that.setData({
            isProcessingRecording: false
          });
          return;
        }

        // 状态检查：如果状态异常，尝试恢复
        if (that.data.recorderState !== 'idle') {
          console.log('录音器状态异常，尝试恢复:', that.data.recorderState);

          // 尝试快速重置录音器状态
          try {
            that.recorderManager.stop();
            that.setData({
              recorderState: 'idle'
            });
            console.log('录音器状态已恢复');
          } catch (error) {
            console.error('   录音器状态恢复失败，终止录音启动:', error);
            that.resetToInitialState({
              isProcessingRecording: false
            });
            that.closeWebSocket(false);
            return;
          }
        }

        console.log('WebSocket连接成功，开始录音', {
          timestamp: new Date().toISOString(),
          recorderState: that.data.recorderState
        });

        try {
          // iOS和Android统一使用基于frameBuffer的流式识别
          console.log('开始基于frameBuffer的流式识别', {
            isIOS: that.data.isIOS,
            timestamp: new Date().toISOString()
          });

          // 根据平台获取音频配置
          let options;
          if (that.data.currentClient == "windows") {
            options = that.getPcAudioConfig();
            console.log('  PC端使用优化配置:', options);
          } else {
            options = that.platformInfo.audioConfig;
            console.log(`${that.platformInfo.platformName}设备使用统一配置:`, options);
          }

          that.recorderManager.start(options);
          console.log('🎤 录音启动成功，开始流式识别');

          // 启动智能录音超时定时器
          that.startIntelligentStopTimer();

          // 立即清除处理标记，提高响应速度
          that.setData({
            isProcessingRecording: false
          });
        } catch (recError) {
          console.error('开始录音失败:', recError);
          wx.showToast({
            title: '启动录音失败，请重试',
            icon: 'none'
          });
          that.closeWebSocket(false); // 录音失败，不发送end标记
          that.setData({
            isluyin: false,
            isanzhu: true,
            textcontent: MESSAGES.INITIAL,
            streamStatus: {
              isStreaming: false,
              isRecording: false,
              isConnected: false
            },
            isProcessingRecording: false
          });
        }
      }).catch(error => {
        console.error('WebSocket连接失败：', error);

        // WebSocket连接失败时，仍然尝试重新连接WebSocket
        console.log('WebSocket连接失败，将尝试重新连接');

        // 重置连接状态但保持WebSocket模式
        that.setData({
          isConnecting: false,
          streamStatus: {
            isStreaming: false,
            isRecording: true,
            isConnected: false
          },
          isProcessingRecording: false
        });

        // 已删除重连逻辑，连接失败时直接使用HTTP备用方案

        wx.showToast({
          title: '连接失败，将使用备用方案',
          icon: 'none'
        });
      });
    }
  },

  // PC端直接开始录音（不使用WebSocket）
  startRecordingDirectly: function () {
    const that = this;

    console.log('PC端直接开始录音');

    // 获取PC端优化配置
    const options = that.getPcAudioConfig();
    console.log('PC端录音配置:', options);

    // 启动录音
    that.recorderManager.start(options);
    console.log('PC端录音启动成功');

    // 启动智能录音超时定时器
    that.startIntelligentStopTimer();
  },

  // 检查识别结果，如果没有结果则恢复初始状态
  checkAndResetIfNoResult: function () {
    const that = this;

    console.log('检查识别结果', {
      timestamp: new Date().toISOString(),
      fullText: that.data.fullText,
      currentText: that.data.currentText,
      lastIntermediateResult: that.data.lastIntermediateResult,
      textcontent: that.data.textcontent
    });

    // 检查是否有有效的识别结果
    // 优化：简化复杂的条件判断逻辑
    const hasResult = that.data.fullText && that.data.fullText.trim() !== '';
    const hasCurrentText = that.data.currentText && that.data.currentText.trim() !== '';
    const hasLastIntermediate = that.data.lastIntermediateResult && that.data.lastIntermediateResult.trim() !== '';
    const hasDisplayText = that.isValidRecognitionText(that.data.textcontent);

    if (!hasResult && !hasCurrentText && !hasLastIntermediate && !hasDisplayText) {
      console.log('没有识别结果，恢复初始状态');

      // 优化：使用公共函数减少重复代码
      that.resetToInitialState({
        fullText: ''
      });
    } else {
      console.log('有识别结果，保持当前状态', {
        hasResult: hasResult,
        hasCurrentText: hasCurrentText,
        hasLastIntermediate: hasLastIntermediate,
        hasDisplayText: hasDisplayText,
        finalText: that.data.currentText || that.data.lastIntermediateResult || that.data.fullText || that.data.textcontent
      });

      // 优先使用当前文本或最后的中间结果作为最终结果
      // 这样可以确保即使在快速结束录音时，也能保留识别结果
      const finalText = that.data.currentText || that.data.lastIntermediateResult || that.data.fullText || that.data.textcontent;

      // 优化：使用简化的验证逻辑
      if (that.isValidRecognitionText(finalText)) {

        // 不在这里添加历史记录，因为已经在流式识别过程中添加了
        // that.addToHistory(finalText);

        // 录音结束后，重置实时识别区域为初始状态
        // 优化：使用公共函数减少重复代码
        that.resetToInitialState({
          fullText: finalText // 保存最终文本到fullText
        });
      } else {
        // 如果没有有效的最终文本，恢复初始状态
        // 优化：使用公共函数减少重复代码
        that.resetToInitialState();
      }
    }
  },

  // 添加清除所有定时器的辅助函数
  // 优化：清理所有定时器的函数，使用批量更新
  clearAllTimers: function () {
    const that = this;
    const timerUpdates = {};

    // 定义所有需要清理的定时器及其类型
    const timers = [{
        name: 'frameCheckTimer',
        type: 'interval'
      },
      {
        name: 'iosWebSocketTimer',
        type: 'interval'
      },
      {
        name: 'wsTimeoutTimer',
        type: 'timeout'
      },
      {
        name: 'connectionTimeoutId',
        type: 'timeout'
      },
      {
        name: 'keepAliveTimer',
        type: 'interval'
      },
      {
        name: 'recordingStopTimeout',
        type: 'timeout'
      },
      {
        name: 'intelligentStopTimer',
        type: 'timeout'
      }
    ];

    // 批量清理定时器
    timers.forEach(timer => {
      if (that.data[timer.name]) {
        // 根据定时器类型选择清理方法
        if (timer.type === 'interval') {
          clearInterval(that.data[timer.name]);
        } else {
          clearTimeout(that.data[timer.name]);
        }
        timerUpdates[timer.name] = null;
      }
    });

    // 批量更新状态，减少setData调用
    if (Object.keys(timerUpdates).length > 0) {
      that.updateState(timerUpdates);
    }

    console.log('所有定时器已清理，批量更新状态');
  },



  // 添加日期格式化函数，参考Flask项目
  dateFormat: function (fmt, date) {
    let ret;
    const opt = {
      "Y+": date.getFullYear().toString(), // 年
      "m+": (date.getMonth() + 1).toString(), // 月
      "d+": date.getDate().toString(), // 日
      "H+": date.getHours().toString(), // 时
      "M+": date.getMinutes().toString(), // 分
      "S+": date.getSeconds().toString() // 秒
    };
    for (let k in opt) {
      ret = new RegExp("(" + k + ")").exec(fmt);
      if (ret) {
        fmt = fmt.replace(ret[1], (ret[1].length == 1) ? (opt[k]) : (opt[k].padStart(ret[1].length, "0")))
      }
    }
    return fmt;
  },



  // iOS和Android统一的WebSocket结果处理函数
  processAndroidWebSocketResult: function (result) {
    const that = this;

    // 添加详细的调试日志
    console.log(' processAndroidWebSocketResult被调用', {
      isIOS: that.data.isIOS,
      hasResult: !!result,
      resultType: typeof result,
      resultContent: result,
      isRecording: that.data.isluyin,
      timestamp: new Date().toISOString()
    });

    // 检查录音状态，如果未在录音则不处理任何结果
    if (!that.data.isluyin) {
      console.log('%c[统一处理] 未在录音状态，忽略识别结果', 'color: #FF5722; font-weight: bold;');
      return;
    }

    // 检查结果是否有效
    if (!result) {
      console.warn('收到空的识别结果');
      return;
    }

    // 处理识别结果
    if (result.result) {
      // 获取识别文本
      let newText = result.result;
      const errCode = result.errCode || "0";

      // 重置智能停止定时器：检测到识别结果，重新计时
      that.resetIntelligentStopTimer();

      // 输出识别结果日志，显示设备类型
      const deviceType = that.data.isIOS ? 'iOS' : 'Android';
      console.log(`%c[${deviceType}WebSocket识别] 识别结果: ` + newText,
        'color: #FF9800; font-weight: bold;',
        '错误码:', errCode);

      // iOS和Android统一的识别结果处理逻辑

      if (errCode === "3") {
        // 中间结果（partial）- 只更新实时识别区域，不添加到历史记录
        console.log(`%c[${deviceType}] 中间结果，更新实时显示`, 'color: #FF9800;');
        that.setData({
          textcontent: newText,
          isanzhu: false
        });
      } else if (errCode === "0") {
        // 最终结果（final）- 添加到历史记录，清空实时显示
        console.log(`%c[${deviceType}] 最终结果，添加到历史记录`, 'color: #4CAF50;');

        // 添加到历史记录
        that.addToHistory(newText);

        // 重置实时识别区域
        that.setData({
          textcontent: MESSAGES.INITIAL,
          isanzhu: true,
          currentText: '',
          lastIntermediateResult: ''
        });
      } else {
        // 其他错误码，只更新实时显示
        console.log(`%c[${deviceType}] 其他结果码: ` + errCode, 'color: #9E9E9E;');
        that.setData({
          textcontent: newText,
          isanzhu: false
        });
      }
    } else if (result.errCode && result.errCode !== "0" && result.errCode !== "3") {
      console.warn('识别错误:', result.errCode);
    }
  },



  // 点击切换录音状态函数
  toggleRecording: function () {
    const that = this;

    console.log('点击录音按钮', {
      timestamp: new Date().toISOString(),
      isRecording: that.data.isluyin,
      recorderState: that.data.recorderState,
      hasSocketConnection: !!that.data.socketTaskId,
      isProcessingRecording: that.data.isProcessingRecording,
      wsConnectionClosed: that.data.wsConnectionClosed
    });

    // 如果正在录音，则停止录音
    if (that.data.isluyin) {
      console.log('正在录音，停止录音');
      that.stopRecording();
    } else {
      // 如果没有录音，则开始录音
      console.log('没有录音，开始录音');
      that.startRecording();
    }
  },

  // 开始录音函数
  startRecording: function () {
    const that = this;

    console.log('startRecording函数被调用', {
      timestamp: new Date().toISOString(),
      isProcessingRecording: that.data.isProcessingRecording,
      recorderState: that.data.recorderState,
      hasRecorderManager: !!that.recorderManager
    });

    // 如果正在处理录音启动，提供用户反馈
    if (that.data.isProcessingRecording) {
      console.log('正在处理上一次录音操作，请稍候再试');
      wx.showToast({
        title: '正在启动录音，请稍候',
        icon: 'loading',
        duration: 1000
      });
      return;
    }

    // 设置处理标记
    that.setData({
      isProcessingRecording: true,
      wsConnectionClosed: false,
      textcontent: '正在录音中，点击结束录音'
    });

    console.log('录音状态已设置，准备启动录音流程');

    // 智能延迟策略：仅在必要时延迟，否则立即启动
    const needsCleanup = that.data.socketTaskId || that.data.recorderState !== 'idle';

    if (needsCleanup) {
      console.log('🔧 检测到需要清理状态', {
        hasWebSocket: !!that.data.socketTaskId,
        recorderState: that.data.recorderState,
        timestamp: new Date().toISOString()
      });

      // 清理现有连接
      if (that.data.socketTaskId) {
        that.closeWebSocket(false); // 不发送end标记
      }

      // 重置录音器状态
      if (that.data.recorderState !== 'idle') {
        try {
          that.recorderManager.stop();
        } catch (error) {
          console.error('停止现有录音失败:', error);
        }
        that.setData({
          recorderState: 'idle'
        });
      }

      // 使用最小延迟确保清理完成
      setTimeout(function () {
        console.log('状态清理完成，启动录音流程');
        that.startRecordingProcess();
      }, 50); // 减少到50ms最小延迟
    } else {
      // 理想状态：立即启动录音流程
      console.log('理想状态，立即启动录音流程');
      that.startRecordingProcess();
    }
  },

  // 停止录音函数
  stopRecording: function () {
    const that = this;

    //  关闭屏幕常亮，恢复正常屏幕设置
    that.disableKeepScreenOn();

    // 清除智能录音超时定时器
    that.clearIntelligentStopTimer();

    console.log('停止录音', {
      timestamp: new Date().toISOString(),
      platform: that.platformInfo.platformName,
      recorderState: that.data.recorderState,
      hasSocketConnection: !!that.data.socketTaskId
    });

    // 保存当前显示的文本，确保中间结果不会丢失
    const currentDisplayText = that.data.textcontent || '';
    const currentText = that.data.currentText || '';
    const lastIntermediate = that.data.lastIntermediateResult || '';

    // 优先使用当前已经识别的文本
    const savedText = currentText || lastIntermediate ||
      (currentDisplayText !== '正在录音中，点击结束录音' ? currentDisplayText : '');

    if (savedText && savedText.trim() !== '') {
      that.setData({
        fullText: savedText
      });
      console.log('已保存当前文本到fullText:', savedText);
    }

    // 设置连接关闭状态
    that.setData({
      wsConnectionClosed: true
    });

    // 清除所有相关定时器
    that.clearAllTimers();

    // iOS设备特殊处理
    if (that.data.isIOS) {
      console.log('iOS设备结束录音');

      if (that.data.socketTaskId) {
        // 停止录音
        if (that.data.recorderState === 'recording') {
          try {
            that.recorderManager.stop();
          } catch (error) {
            console.error('停止录音失败:', error);
            that.setData({
              recorderState: 'idle'
            });
          }
        }

        // 已删除最后音频数据发送，统一使用onFrameRecorded流式识别

        // 发送end标记后关闭WebSocket
        setTimeout(function () {
          that.closeWebSocket(true); // 发送end标记

          // 延迟检查识别结果
          setTimeout(function () {
            that.checkAndResetIfNoResult();
          }, DELAYS.RESULT_CHECK);
        }, DELAYS.WEBSOCKET_CLOSE);
      }
    } else {
      // 非iOS设备，使用常规方式停止录音
      if (that.recorderManager) {
        console.log('停止录音', {
          timestamp: new Date().toISOString(),
          hasSocketConnection: !!that.data.socketTaskId
        });

        try {
          if (that.data.recorderState === 'recording') {
            that.recorderManager.stop();
          } else {
            that.setData({
              recorderState: 'idle'
            });
          }
        } catch (error) {
          console.error('停止录音失败：', error);
          wx.showToast({
            title: '停止录音失败',
            icon: 'none'
          });
          that.setData({
            recorderState: 'idle'
          });
        }
      }

      // 关闭WebSocket连接
      that.closeWebSocket(true); // 发送end标记

      // 延迟检查识别结果
      setTimeout(function () {
        that.checkAndResetIfNoResult();
      }, DELAYS.RESULT_CHECK);
    }

    // 设置最终状态
    that.setData({
      isluyin: false,
      streamStatus: {
        isStreaming: false,
        isRecording: false,
        isConnected: false
      },
      isProcessingRecording: false
    });
  }
})